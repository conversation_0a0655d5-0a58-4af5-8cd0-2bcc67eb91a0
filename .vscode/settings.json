{"files.associations": {"cctype": "cpp", "cmath": "cpp", "cstddef": "cpp", "cstdio": "cpp", "cstdlib": "cpp", "cstring": "cpp", "cwchar": "cpp", "array": "cpp", "atomic": "cpp", "bit": "cpp", "compare": "cpp", "complex": "cpp", "concepts": "cpp", "cstdint": "cpp", "list": "cpp", "map": "cpp", "vector": "cpp", "exception": "cpp", "algorithm": "cpp", "functional": "cpp", "iterator": "cpp", "memory": "cpp", "memory_resource": "cpp", "optional": "cpp", "random": "cpp", "string": "cpp", "string_view": "cpp", "tuple": "cpp", "type_traits": "cpp", "utility": "cpp", "fstream": "cpp", "initializer_list": "cpp", "iomanip": "cpp", "iosfwd": "cpp", "iostream": "cpp", "istream": "cpp", "limits": "cpp", "new": "cpp", "ostream": "cpp", "stdexcept": "cpp", "streambuf": "cpp", "typeinfo": "cpp", "unordered_map": "cpp", "unordered_set": "cpp", "hash_map": "cpp", "hash_set": "cpp", "ranges": "cpp", "typeindex": "cpp", "chrono": "cpp", "clocale": "cpp", "csignal": "cpp", "cstdarg": "cpp", "ctime": "cpp", "cwctype": "cpp", "any": "cpp", "strstream": "cpp", "bitset": "cpp", "cfenv": "cpp", "charconv": "cpp", "codecvt": "cpp", "condition_variable": "cpp", "deque": "cpp", "forward_list": "cpp", "set": "cpp", "numeric": "cpp", "ratio": "cpp", "regex": "cpp", "source_location": "cpp", "system_error": "cpp", "future": "cpp", "mutex": "cpp", "shared_mutex": "cpp", "sstream": "cpp", "stop_token": "cpp", "thread": "cpp", "cinttypes": "cpp", "variant": "cpp"}, "python.autoComplete.extraPaths": ["/opt/ros/noetic/lib/python3/dist-packages"], "python.analysis.extraPaths": ["/opt/ros/noetic/lib/python3/dist-packages"]}