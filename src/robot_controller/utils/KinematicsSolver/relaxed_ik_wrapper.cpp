/**
 * @file cpp_wrapper.cpp
 * @brief C++ wrapper for the relaxed_ik_lib Rust library
 *
 * This file provides a C++ interface to the relaxed_ik_lib Rust library,
 * implementing the same functionality as the python_wrapper.py file.
 */

#include "relaxed_ik_wrapper/relaxed_ik_wrapper.h"
#include <iostream>
#include <dlfcn.h>
#include <cstring>
#include <memory>
#include <algorithm>
#include "ros/package.h"

// Define the same structures as in the Python wrapper
struct Opt {
    double* data;
    int length;
};

// Forward declaration of the opaque RelaxedIKS struct
struct RelaxedIKS;

// Function pointer types for the library functions
typedef RelaxedIKS* (*RelaxedIKNewFunc)(const char*);
typedef void (*RelaxedIKFreeFunc)(RelaxedIKS*);
typedef Opt (*SolvePositionFunc)(RelaxedIKS*, double*, int, double*, int, double*, int);
typedef Opt (*SolveVelocityFunc)(RelaxedIKS*, double*, int, double*, int, double*, int);
typedef void (*ResetFunc)(RelaxedIKS*, double*, int);

// Private implementation class
class RelaxedIKRust::Impl {
public:
    void* lib_handle;
    RelaxedIKS* obj;

    // Function pointers
    RelaxedIKNewFunc relaxed_ik_new;
    RelaxedIKFreeFunc relaxed_ik_free;
    SolvePositionFunc solve_position;
    SolveVelocityFunc solve_velocity;
    ResetFunc reset;

    Impl(const std::string& setting_file_path) {
        // Get the directory of the current file
        std::string path_package = ros::package::getPath("robot_controller");

        // Load the library
        std::string lib_path = path_package + "/lib/librelaxed_ik_lib.so";
        lib_handle = dlopen(lib_path.c_str(), RTLD_LAZY);

        if (!lib_handle) {
            std::cerr << "Error loading library: " << dlerror() << std::endl;
            throw std::runtime_error("Failed to load relaxed_ik_lib library");
        }

        // Load the functions
        relaxed_ik_new = (RelaxedIKNewFunc)dlsym(lib_handle, "relaxed_ik_new");
        relaxed_ik_free = (RelaxedIKFreeFunc)dlsym(lib_handle, "relaxed_ik_free");
        solve_position = (SolvePositionFunc)dlsym(lib_handle, "solve_position");
        solve_velocity = (SolveVelocityFunc)dlsym(lib_handle, "solve_velocity");
        reset = (ResetFunc)dlsym(lib_handle, "reset");

        // Check for errors
        const char* dlsym_error = dlerror();
        if (dlsym_error) {
            std::cerr << "Error loading symbols: " << dlsym_error << std::endl;
            dlclose(lib_handle);
            throw std::runtime_error("Failed to load symbols from relaxed_ik_lib library");
        }

        // Create the relaxed_ik object
        if (setting_file_path.empty()) {
            obj = relaxed_ik_new(nullptr);
        } else {
            obj = relaxed_ik_new(setting_file_path.c_str());
        }

        if (!obj) {
            dlclose(lib_handle);
            throw std::runtime_error("Failed to create relaxed_ik object");
        }
    }

    ~Impl() {
        if (obj) {
            relaxed_ik_free(obj);
            obj = nullptr;
        }

        if (lib_handle) {
            dlclose(lib_handle);
            lib_handle = nullptr;
        }
    }
};

// Constructor implementation
RelaxedIKRust::RelaxedIKRust(const std::string& setting_file_path)
    : pimpl(new Impl(setting_file_path)) {
}

// Destructor implementation
RelaxedIKRust::~RelaxedIKRust() {
    // Smart pointer will automatically delete the implementation
}

// solve_position implementation
std::vector<double> RelaxedIKRust::solve_position(
    const std::vector<double>& positions,
    const std::vector<double>& orientations,
    const std::vector<double>& tolerances
) {
    // Create arrays for the C function
    double* pos_arr = new double[positions.size()];
    double* quat_arr = new double[orientations.size()];
    double* tole_arr = new double[tolerances.size()];

    // Copy data to arrays
    std::copy(positions.begin(), positions.end(), pos_arr);
    std::copy(orientations.begin(), orientations.end(), quat_arr);
    std::copy(tolerances.begin(), tolerances.end(), tole_arr);

    // Call the function
    Opt xopt = pimpl->solve_position(
        pimpl->obj,
        pos_arr, positions.size(),
        quat_arr, orientations.size(),
        tole_arr, tolerances.size()
    );

    // Copy the result to a vector
    std::vector<double> result(xopt.data, xopt.data + xopt.length);

    // Clean up
    delete[] pos_arr;
    delete[] quat_arr;
    delete[] tole_arr;

    return result;
}

// solve_velocity implementation
std::vector<double> RelaxedIKRust::solve_velocity(
    const std::vector<double>& linear_velocities,
    const std::vector<double>& angular_velocities,
    const std::vector<double>& tolerances
) {
    // Create arrays for the C function
    double* linear_arr = new double[linear_velocities.size()];
    double* angular_arr = new double[angular_velocities.size()];
    double* tole_arr = new double[tolerances.size()];

    // Copy data to arrays
    std::copy(linear_velocities.begin(), linear_velocities.end(), linear_arr);
    std::copy(angular_velocities.begin(), angular_velocities.end(), angular_arr);
    std::copy(tolerances.begin(), tolerances.end(), tole_arr);

    // Call the function
    Opt xopt = pimpl->solve_velocity(
        pimpl->obj,
        linear_arr, linear_velocities.size(),
        angular_arr, angular_velocities.size(),
        tole_arr, tolerances.size()
    );

    // Copy the result to a vector
    std::vector<double> result(xopt.data, xopt.data + xopt.length);

    // Clean up
    delete[] linear_arr;
    delete[] angular_arr;
    delete[] tole_arr;

    return result;
}

// reset_solver implementation
void RelaxedIKRust::reset_solver(const std::vector<double>& joint_state) {
    // Create array for the C function
    double* js_arr = new double[joint_state.size()];

    // Copy data to array
    std::copy(joint_state.begin(), joint_state.end(), js_arr);

    // Call the function
    pimpl->reset(pimpl->obj, js_arr, joint_state.size());

    // Clean up
    delete[] js_arr;
}

/**
 * @brief 初始化RelaxedIK求解器并返回求解器对象
 *
 * @param setting_file_path 设置文件路径
 * @return RelaxedIKRust* 指向初始化好的RelaxedIK求解器的指针，如果初始化失败则返回nullptr
 */
RelaxedIKRust* RelaxedIKRust::initialize(const std::string& setting_file_path) {
    try {
        std::cout << "开始初始化 RelaxedIKRust..." << std::endl;

        // 创建 RelaxedIKRust 对象
        RelaxedIKRust* relaxed_ik = new RelaxedIKRust(setting_file_path);
        std::cout << "RelaxedIKRust 初始化成功!" << std::endl;

        return relaxed_ik;
    } catch (const std::exception& e) {
        std::cerr << "初始化错误: " << e.what() << std::endl;
        return nullptr;
    }
}

// Simple test function
int main() {
    try {
        std::cout << "开始测试 RelaxedIKRust C++ 封装..." << std::endl;

        // 初始化 RelaxedIKRust 对象
        RelaxedIKRust* relaxed_ik = RelaxedIKRust::initialize(".");
        if (!relaxed_ik) {
            std::cerr << "初始化失败，退出测试" << std::endl;
            return 1;
        }

        // 测试位置逆解
        std::vector<double> positions = {0.5, 0.0, 0.5};  // 假设只有一个末端执行器
        std::vector<double> orientations = {0.0, 0.0, 0.0, 1.0};  // 四元数 xyzw 格式
        std::vector<double> tolerances(6, 0.01);  // 6个容差值

        std::cout << "测试位置逆解..." << std::endl;
        std::vector<double> joint_solution = relaxed_ik->solve_position(positions, orientations, tolerances);

        std::cout << "位置逆解结果:" << std::endl;
        for (size_t i = 0; i < joint_solution.size(); ++i) {
            std::cout << "关节 " << i << ": " << joint_solution[i] << std::endl;
        }

        // 测试速度逆解
        std::vector<double> linear_velocities = {0.1, 0.0, 0.0};  // x方向线速度
        std::vector<double> angular_velocities = {0.0, 0.0, 0.1};  // z方向角速度

        std::cout << "测试速度逆解..." << std::endl;
        std::vector<double> joint_velocities = relaxed_ik->solve_velocity(linear_velocities, angular_velocities, tolerances);

        std::cout << "速度逆解结果:" << std::endl;
        for (size_t i = 0; i < joint_velocities.size(); ++i) {
            std::cout << "关节 " << i << " 速度: " << joint_velocities[i] << std::endl;
        }

        // 测试重置功能
        std::cout << "测试重置功能..." << std::endl;
        std::vector<double> initial_state(joint_solution.size(), 0.0);  // 全零初始状态
        relaxed_ik->reset_solver(initial_state);
        std::cout << "重置完成" << std::endl;

        // 清理资源
        delete relaxed_ik;

        return 0;
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
        return 1;
    }
}
