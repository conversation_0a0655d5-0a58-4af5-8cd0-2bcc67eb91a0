/*
机械臂运动学运算
    - TRAC-IK求解器 
*/

#include "robot_controller/def_class.h"

namespace KINEMATICS {
    /**
     * @brief TRAC_IK_Solver类的构造函数
     * @details 根据机械臂的类型，加载对应的yaml配置文件，初始化KDL::Tree对象，以及获取链和关节上下限，初始化TRAC-IK求解器
     * @param robot_type 机械臂类型（std::string），目前只有S1，适配绳驱S1机器人的机械臂
     * @param side 机械臂侧别（std::string），right或left，适配双臂机器人
     * @param solve_type 求解类型（TRAC_IK::SolveType），默认为Distance，可选项TRAC_IK::Distance, TRAC_IK::Speed, TRAC_IK::Manip1, TRAC_IK::Manip2
     */
    TRAC_IK_Solver::TRAC_IK_Solver(std::string robot_type, std::string side, TRAC_IK::SolveType solve_type){ 
        arm_type = robot_type;
        arm_side = side;

        std::string path_pkg = ros::package::getPath("robot_controller");
        path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
        base_link = "base_arm";
        if(arm_side == "right"){
            tip_link = "r_Link7";
        }else if(arm_side == "left"){
            tip_link = "l_Link7";
        }else{
            ROS_ERROR("Arm side invalid!");
            exit(0);
        }

        // 根据机器人类型设置 z 偏移
        if(arm_type == "S1"){
            z_bias = 1.225;
        }else{
            z_bias = 1.225;
            ROS_ERROR("Robot type invalid!");
            exit(0);
        }

        std::ifstream urdf_file(path_urdf);
        std::stringstream urdf_string;
        urdf_string << urdf_file.rdbuf();
        ros::param::set("robot_description", urdf_string.str());
        iksolver = new TRAC_IK::TRAC_IK(base_link, tip_link, "robot_description", timeout, eps, solve_type);
    }

    TRAC_IK_Solver::~TRAC_IK_Solver(){
        // delete iksolver;
    }

    /**
     * @brief TRAC-IK逆运动学求解。
     * @details 根据腕关节处的位姿和臂角求解7个关节的角度，可选是否进行精度和耗时测试。
     * @param cart_pos 目标末端位置。
     * @param eular 目标末端姿态（欧拉角，顺序zyx）。
     * @param qpos_init 初始猜测关节角度，默认值为0。
     * @param accuracy_test 是否进行准确度测试（可选），布尔值。计算求解出的那组关节角度经过正运动学计算后，末端位姿与目标位姿的差距。
     * @param time_test 是否进行耗时测试（可选），布尔值。计算逆解耗时。
     */
    JntPos TRAC_IK_Solver::ik_solver(Eigen::Vector3d cart_pos, Eigen::Vector3d eular, Vector7d qpos_init, bool accuracy_test, bool time_test){
        auto start = std::chrono::steady_clock::now();  // 记录开始时间

        // 构造目标末端位姿（旋转 + 平移）
        // KDL 使用 RPY 顺序：滚转-俯仰-偏航，输入顺序需反转
        KDL::Rotation rot = KDL::Rotation::RPY(eular(2), eular(1), eular(0));
        KDL::Vector vec(cart_pos(0), cart_pos(1), cart_pos(2) - z_bias);  // 平移部分加上 z 偏移
        KDL::Frame T_base_goal(rot, vec);  // 目标末端位姿

        bool kinematics_status;
        KDL::JntArray jointpositions(nj); // IK 解的关节角
        KDL::JntArray q_init(nj);         // 初始猜测

        ROS_INFO_STREAM("FLAG1");
        // 设置初始猜测值 q_init
        for(unsigned int i = 0; i < nj; i++){
            q_init(i) = qpos_init(i,0);
        }
        ROS_INFO_STREAM("FLAG2");

        JntPos res;  // 返回结果结构体
        Eigen::VectorXd theta(nj); 

        // 执行逆运动学求解：输入初始姿态和目标位姿，输出关节角
        kinematics_status = iksolver->CartToJnt(q_init, T_base_goal, jointpositions) >= 0;
        ROS_INFO_STREAM("FLAG3");

        
        if(kinematics_status){  // 求解成功
            for(unsigned int i = 0; i < nj; i++){
                theta(i) = jointpositions(i);
            }
            res.solved_flag = 1;
            res.theta = theta;

            // 如果开启精度测试，计算 FK 验证 IK 结果
            if (accuracy_test) {
                KDL::Frame res_fk = fk_solver(theta);  // 计算正向运动学

                // 1. 计算位置误差
                double pos_error = (T_base_goal.p - res_fk.p).Norm();

                // 2. 计算姿态误差（角度）
                KDL::Rotation R_error = T_base_goal.M.Inverse() * res_fk.M;
                KDL::Vector rot_axis;
                double angle_error_rad;
                R_error.GetRotAngle(rot_axis, angle_error_rad);  // 提取旋转角度
                double angle_error_deg = angle_error_rad * 180.0 / M_PI;

                // 打印误差信息
                ROS_INFO_STREAM(GREEN << "[TRAC-IK Accuracy Test] Position error: " << pos_error << " m" << RESET);
                ROS_INFO_STREAM(GREEN << "[TRAC-IK Accuracy Test] Orientation error: " << angle_error_deg << " deg" << RESET);
            }
        } else {  // 求解失败
            res.error_flag = 1;
            ROS_WARN("[TRAC-IK]ik solver failed!");
            return res;
        }

        // 记录结束时间并计算耗时
        auto end = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        
        // 打印运行时间（秒）
        if(time_test)
            ROS_INFO_STREAM(GREEN << "[TRAC-IK Time Test] Cost " << (float)duration/1000000 << "s" << RESET);
            ROS_INFO_STREAM(GREEN << "[TRAC-IK] Result " << res.theta.transpose() << RESET);

        return res;
    }

    /**
     * @brief KDL正运动学求解
     * @details 根据臂的关节角度解算末端位姿
     * @param jnt_pos 关节角度
     * @param print_res 是否打印结果
     */
    KDL::Frame TRAC_IK_Solver::fk_solver(Eigen::VectorXd jnt_pos, bool print_res){

        fksolver.updateInternalDataStructures();
        
        KDL::JntArray jointposition = KDL::JntArray(nj);
        for(int i = 0; i < nj; i++){
            jointposition(i) = jnt_pos(i);
        }

        KDL::Frame fk_res;
        if(!fksolver.JntToCart(jointposition,fk_res)){
            if(print_res){
                KDL::Vector position;
                KDL::Rotation rotation;
                double roll, pitch, yaw;
                double qx, qy, qz, qw;
                
                position = fk_res.p;
                rotation = fk_res.M;
                rotation.GetRPY(roll, pitch, yaw);
                rotation.GetQuaternion(qx, qy, qz, qw);
                ROS_INFO("position: [%.4f, %.4f, %.4f]", position.x(), position.y(), position.z());
                ROS_INFO("rpy: [%.4f, %.4f, %.4f]", roll, pitch, yaw);
                ROS_INFO("quaternion: [%.4f, %.4f, %.4f, %.4f]", qx, qy, qz, qw);
            }
        } 
        else{ 
            ROS_WARN("Error:could not calculate forward kinematics");
        }
        return fk_res; 
    }
}