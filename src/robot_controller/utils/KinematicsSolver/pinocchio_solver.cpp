#include "robot_controller/def_class.h"

namespace KINEMATICS {
    PinocchioSolver::PinocchioSolver(std::string robot_type, std::string side) {
        path_urdf_ = ros::package::getPath("robot_controller") + "/description/urdf/S1_robot.urdf";

        try {
            // 加载完整的机器人模型
            pinocchio::urdf::buildModel(path_urdf_, full_model_, false, true);

            // 构建右臂模型
            if (buildArmModel(full_model_, side)) {
                // 检查末端执行器是否存在
                if (arm_model_.existFrame(end_effector_name_)) {
                    end_effector_id_ = arm_model_.getFrameId(end_effector_name_);

                    // 初始化关节限制（S1机器人右臂关节限制）
                    q_min_.resize(arm_model_.nq);
                    q_max_.resize(arm_model_.nq);

                    // 设置默认关节角度限制（单位：弧度）
                    q_min_ << -3.14159, -1.5708, -3.14159, -2.0944, -3.14159, -1.5708, -3.14159;  // 下限
                    q_max_ <<  3.14159,  1.5708,  3.14159,  2.0944,  3.14159,  1.5708,  3.14159;  // 上限

                    initialized_ = true;
                    std::cout << "右臂逆运动学求解器初始化成功" << std::endl;
                    std::cout << "右臂关节数量: " << arm_model_.nq << std::endl;
                    std::cout << "末端执行器: " << end_effector_name_ << std::endl;
                    std::cout << "关节限制已设置" << std::endl;
                } else {
                    std::cerr << "错误：未找到末端执行器 " << end_effector_name_ << std::endl;
                }
            } else {
                std::cerr << "错误：构建右臂模型失败" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cerr << "初始化右臂逆运动学求解器时发生异常: " << e.what() << std::endl;
        }


    }

    bool PinocchioSolver::buildArmModel(const pinocchio::Model& full_model, std::string arm_side){
        // 通过排除法确定要锁定的关节：定义需要保留的关节，其余全部锁定
        std::vector<std::string> joints_to_keep;
        if(arm_side == "right"){
            // 只保留右臂的7个关节
            joints_to_keep.push_back("r_joint1");
            joints_to_keep.push_back("r_joint2");
            joints_to_keep.push_back("r_joint3");
            joints_to_keep.push_back("r_joint4");
            joints_to_keep.push_back("r_joint4_mimic");
            joints_to_keep.push_back("r_joint5");
            joints_to_keep.push_back("r_joint6");
            joints_to_keep.push_back("r_joint7");

            // 设置末端执行器名称
            end_effector_name_ = "r_Link7";
        }
        else if(arm_side == "left"){
            // 只保留左臂的7个关节
            joints_to_keep.push_back("l_joint1");
            joints_to_keep.push_back("l_joint2");
            joints_to_keep.push_back("l_joint3");
            joints_to_keep.push_back("l_joint4");
            joints_to_keep.push_back("l_joint4_mimic");
            joints_to_keep.push_back("l_joint5");
            joints_to_keep.push_back("l_joint6");
            joints_to_keep.push_back("l_joint7");

            // 设置末端执行器名称
            end_effector_name_ = "l_Link7";
        }
        else{
            std::cerr << "错误：未知的机械臂侧" << std::endl;
            return false;
        }

        // 通过排除法生成要锁定的关节列表
        std::vector<std::string> joints_to_lock_names;
        // 遍历模型中的所有关节，排除需要保留的关节
        for(int i = 1; i < full_model.njoints; i++) {  // 从1开始，跳过universe关节
            std::string joint_name = full_model.names[i];

            // 检查当前关节是否在保留列表中
            bool should_keep = false;
            for(const std::string& keep_joint : joints_to_keep) {
                if(joint_name == keep_joint) {
                    should_keep = true;
                    break;
                }
            }

            // 如果不在保留列表中，则添加到锁定列表
            if(!should_keep) {
                joints_to_lock_names.push_back(joint_name);
            }
        }


        try {
            // 获取需要锁定的关节列表
            std::vector<pinocchio::JointIndex> joints_to_lock_ids;
            for (const auto& joint_name : joints_to_lock_names) {
                if (full_model.existJointName(joint_name)) {
                    joints_to_lock_ids.push_back(full_model.getJointId(joint_name));
                }
            }

            // 设置锁定关节的位置为全0
            Eigen::VectorXd q_locked = Eigen::VectorXd::Zero(full_model.nq);

            // 构建单臂模型
            arm_model_ = pinocchio::buildReducedModel(full_model, joints_to_lock_ids, q_locked);
            arm_data_ = pinocchio::Data(arm_model_);
        } catch (const std::exception& e) {
            std::cerr << "构建右臂模型时发生异常: " << e.what() << std::endl;
            return false;
        }

        return true;
    }

}