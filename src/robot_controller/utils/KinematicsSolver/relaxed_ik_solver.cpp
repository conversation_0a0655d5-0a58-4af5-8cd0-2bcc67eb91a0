#include "robot_controller/Def_Class.h"

KINEMATICS::SpatialTransform st;
namespace KINEMATICS {
    /**
     * @brief Relaxed_IK_Solver类的构造函数
     * @details 根据机械臂的类型，加载对应的yaml配置文件，初始化KDL::Tree对象，以及获取链和关节上下限，初始化Relaxed-IK求解器
     * @param robot_type 机械臂类型（std::string），gen2或gen3，适配启江2号和2C机器人的机械臂
     * @param side 机械臂侧别（std::string），right或left，适配双臂机器人
     */
    Relaxed_IK_Solver::Relaxed_IK_Solver(std::string robot_type, std::string side){ 
        std::string path_package = ros::package::getPath("robot_controller");
        setting_file_path = path_package + "/config/relaxed_ik_" + side + "_arm_" + robot_type + "_settings.yaml";

        relaxed_ik_solver = RelaxedIKRust::initialize(setting_file_path);

        tolerances = {0.0001, 0.0001, 0.0001, 0.01, 0.01, 0.01};

        config = YAML::LoadFile(setting_file_path);
        path_urdf = config["urdf"].as<std::string>();
        base_link = config["base_links"][0].as<std::string>();
        tip_link  = config["ee_links"][0].as<std::string>();

        if(!kdl_parser::treeFromFile(path_urdf, my_tree)){
            ROS_ERROR_STREAM("Failed to load URDF from: " << path_urdf);
            exit(0);
        }

        // 获取链
        if (!my_tree.getChain(base_link, tip_link, chain)) {
            ROS_ERROR("Failed to extract KDL chain from tree");
            exit(0);
        }
        nj = chain.getNrOfJoints();
    }

    Relaxed_IK_Solver::~Relaxed_IK_Solver(){}

    /**
     * @brief Relaxed-IK逆运动学求解。
     * @details 根据腕关节处的位姿和臂角求解7个关节的角度，可选是否进行精度和耗时测试。
     * @param cart_pos 目标末端位置。
     * @param eular 目标末端姿态（欧拉角，顺序zyx）。
     * @param accuracy_test 是否进行准确度测试（可选），布尔值。计算求解出的那组关节角度经过正运动学计算后，末端位姿与目标位姿的差距。
     * @param time_test 是否进行耗时测试（可选），布尔值。计算逆解耗时。
     */
    JntPos Relaxed_IK_Solver::ik_solver(Eigen::Vector3d cart_pos, Eigen::Vector3d eular, bool accuracy_test, bool time_test){
        auto start = std::chrono::steady_clock::now();  // 记录开始时间

        Eigen::Vector4d quat = st.eulerAnglesToQuaternion(eular,false,"xyz");
        std::vector<double> positions = {cart_pos(0), cart_pos(1), cart_pos(2)};
        std::vector<double> orientations = {quat(0), quat(1), quat(2), quat(3)};

        std::vector<double> joint_solution = relaxed_ik_solver->solve_position(positions, orientations, tolerances);

        JntPos res;
        res.solved_flag = 1;
        res.theta = Eigen::Map<Eigen::VectorXd>(joint_solution.data(), joint_solution.size());

         // 如果开启精度测试，计算 FK 验证 IK 结果
        if (accuracy_test) {
            KDL::Rotation rot = KDL::Rotation::RPY(eular(2), eular(1), eular(0));
            KDL::Vector vec(cart_pos(0), cart_pos(1), cart_pos(2));  // 平移部分加上 z 偏移
            KDL::Frame T_base_goal(rot, vec);  // 目标末端位姿

            KDL::Frame res_fk = fk_solver(res.theta);  // 计算正向运动学

            // 1. 计算位置误差
            double pos_error = (T_base_goal.p - res_fk.p).Norm();

            // 2. 计算姿态误差（角度）
            KDL::Rotation R_error = T_base_goal.M.Inverse() * res_fk.M;
            KDL::Vector rot_axis;
            double angle_error_rad;
            R_error.GetRotAngle(rot_axis, angle_error_rad);  // 提取旋转角度
            double angle_error_deg = angle_error_rad * 180.0 / M_PI;

            // 打印误差信息
            ROS_INFO_STREAM(GREEN << "[Relaxed-IK Accuracy Test] Position error: " << pos_error << " m" << RESET);
            ROS_INFO_STREAM(GREEN << "[Relaxed-IK Accuracy Test] Orientation error: " << angle_error_deg << " deg" << RESET);
        }
        // 记录结束时间并计算耗时
        auto end = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
        
        // 打印运行时间（秒）
        if(time_test)
            ROS_INFO_STREAM(GREEN << "[Relaxed-ik Time Test] Cost " << (float)duration/1000000 << "s" << RESET);

        return res;
    }

    JntPos Relaxed_IK_Solver::ik_solver(Eigen::Matrix<double, 6, 1> ee_vel){
        std::vector<double> linear_velocities = {ee_vel(0), ee_vel(1), ee_vel(2)};
        std::vector<double> angular_velocities = {ee_vel(3), ee_vel(4), ee_vel(5)};

        std::vector<double> joint_solution = relaxed_ik_solver->solve_velocity(linear_velocities, angular_velocities, tolerances);

        JntPos res;
        res.solved_flag = 1;
        res.theta = Eigen::Map<Eigen::VectorXd>(joint_solution.data(), joint_solution.size());

        return res;
    }

    /**
     * @brief KDL正运动学求解
     * @details 根据臂的关节角度解算末端位姿
     * @param jnt_pos 关节角度
     * @param print_res 是否打印结果
     */
    KDL::Frame Relaxed_IK_Solver::fk_solver(Eigen::VectorXd jnt_pos, bool print_res){

        fksolver.updateInternalDataStructures();
        
        KDL::JntArray jointposition = KDL::JntArray(nj);
        for(int i = 0; i < nj; i++){
            jointposition(i) = jnt_pos(i);
        }

        KDL::Frame fk_res;
        if(!fksolver.JntToCart(jointposition,fk_res)){
            if(print_res){
                KDL::Vector position;
                KDL::Rotation rotation;
                double roll, pitch, yaw;
                double qx, qy, qz, qw;
                
                position = fk_res.p;
                rotation = fk_res.M;
                rotation.GetRPY(roll, pitch, yaw);
                rotation.GetQuaternion(qx, qy, qz, qw);
                ROS_INFO("position: [%.4f, %.4f, %.4f]", position.x(), position.y(), position.z());
                ROS_INFO("rpy: [%.4f, %.4f, %.4f]", roll, pitch, yaw);
                ROS_INFO("quaternion: [%.4f, %.4f, %.4f, %.4f]", qx, qy, qz, qw);
            }
        } 
        else{ 
            ROS_WARN("Error:could not calculate forward kinematics");
        }
        return fk_res; 
    }
}
