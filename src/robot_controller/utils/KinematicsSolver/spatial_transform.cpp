/*
姿态变换和单位转换
*/

#include "robot_controller/def_class.h"

namespace KINEMATICS {
    /**
     * @brief 获得绕x轴旋转angle角度的旋转矩阵
     * @param angle 旋转角度
     * @param degrees 是否为角度，默认值为false，表示默认为弧度；若为true，则表示传入值为角度，函数会自动将角度转为弧度
     * @return 旋转矩阵
     */
    Eigen::Matrix3d SpatialTransform::rot_x(double angle, bool degrees) {
        if(degrees) angle = angle * M_PI / 180.0;
        Eigen::Matrix3d res;
        res << 1, 0, 0,
                0, cos(angle), -sin(angle),
                0, sin(angle), cos(angle);
        return res;
    }
    /**
     * @brief 获得绕y轴旋转angle角度的旋转矩阵
     * @param angle 旋转角度
     * @param degrees 是否为角度，默认值为false，表示默认为弧度；若为true，则表示传入值为角度，函数会自动将角度转为弧度
     * @return 旋转矩阵
     */
    Eigen::Matrix3d SpatialTransform::rot_y(double angle, bool degrees) {
        if(degrees) angle = angle * M_PI / 180.0;
        Eigen::Matrix3d res;
        res << cos(angle), 0, sin(angle),
                0, 1, 0,
                -sin(angle), 0, cos(angle);
        return res;
    }
    /**
     * @brief 获得绕z轴旋转angle角度的旋转矩阵
     * @param angle 旋转角度
     * @param degrees 是否为角度，默认值为false，表示默认为弧度；若为true，则表示传入值为角度，函数会自动将角度转为弧度
     * @return 旋转矩阵
     */
    Eigen::Matrix3d SpatialTransform::rot_z(double angle, bool degrees) {
        if(degrees) angle = angle * M_PI / 180.0;
        Eigen::Matrix3d res;
        res << cos(angle), -sin(angle), 0,
                sin(angle), cos(angle), 0,
                0, 0, 1;
        return res;
    }

    /**
     * @brief 四元数转欧拉角
     * @attention Eigen::Quaterniond(w, x, y, z) 构造时，内部是按 (x, y, z, w) 存储的
     * @param quat 四元数(xyzw)
     * @param degrees 是否为角度，默认值为true，表示欧拉角默认为角度值；若为false，则表示欧拉角为弧度值
     * @param sequence 欧拉角旋转顺序，默认为zyx
     * @return 欧拉角
     */
    Eigen::Vector3d SpatialTransform::quaternionToEulerAngles(Eigen::Vector4d quat, bool degrees, std::string sequence) {
        Eigen::Quaterniond q(quat(3),quat(0),quat(1),quat(2));
        Eigen::Vector3d euler = rotationMatrixToEulerAngles(q.toRotationMatrix(), degrees, sequence);
        return euler;
    }

    /**
     * @brief 欧拉角转四元数
     * @param euler 欧拉角
     * @param degrees 是否为角度，默认值为true，表示欧拉角默认为角度值；若为false，则表示欧拉角为弧度值
     * @param sequence 欧拉角旋转顺序，默认为zyx
     * @return 四元数，顺序为xyzw
     */
    Eigen::Vector4d SpatialTransform::eulerAnglesToQuaternion(Eigen::Vector3d euler, bool degrees, std::string sequence) {
        if(degrees) {
            euler = euler / 180.0 * M_PI;
        }
        Eigen::AngleAxisd yawAngle;
        Eigen::AngleAxisd pitchAngle;
        Eigen::AngleAxisd rollAngle;
        
        if(sequence == "xyz") {
            // XYZ顺序
            yawAngle = Eigen::AngleAxisd(euler(0), Eigen::Vector3d::UnitX());
            pitchAngle = Eigen::AngleAxisd(euler(1), Eigen::Vector3d::UnitY());
            rollAngle = Eigen::AngleAxisd(euler(2), Eigen::Vector3d::UnitZ());
        }else if(sequence == "yzx") {
            // YZX顺序
            yawAngle = Eigen::AngleAxisd(euler(0), Eigen::Vector3d::UnitY());
            pitchAngle = Eigen::AngleAxisd(euler(1), Eigen::Vector3d::UnitZ());
            rollAngle = Eigen::AngleAxisd(euler(2), Eigen::Vector3d::UnitX());
        }else if(sequence == "zxy") {
            // ZXY顺序
            yawAngle = Eigen::AngleAxisd(euler(0), Eigen::Vector3d::UnitZ());
            pitchAngle = Eigen::AngleAxisd(euler(1), Eigen::Vector3d::UnitX());
            rollAngle = Eigen::AngleAxisd(euler(2), Eigen::Vector3d::UnitY());
        }else if(sequence == "xzy") {
            // XZY顺序
            yawAngle = Eigen::AngleAxisd(euler(0), Eigen::Vector3d::UnitX());
            pitchAngle = Eigen::AngleAxisd(euler(1), Eigen::Vector3d::UnitZ());
            rollAngle = Eigen::AngleAxisd(euler(2), Eigen::Vector3d::UnitY());
        }else if(sequence == "yxz"){
            // YXZ顺序
            yawAngle = Eigen::AngleAxisd(euler(0), Eigen::Vector3d::UnitY());
            pitchAngle = Eigen::AngleAxisd(euler(1), Eigen::Vector3d::UnitX());
            rollAngle = Eigen::AngleAxisd(euler(2), Eigen::Vector3d::UnitZ());
        }else{
            // ZYX顺序(默认)
            yawAngle = Eigen::AngleAxisd(euler(0), Eigen::Vector3d::UnitZ());
            pitchAngle = Eigen::AngleAxisd(euler(1), Eigen::Vector3d::UnitY());
            rollAngle = Eigen::AngleAxisd(euler(2), Eigen::Vector3d::UnitX());
        }
        // Eigen::Quaterniond q = rollAngle * pitchAngle * yawAngle;
        Eigen::Quaterniond q = yawAngle * pitchAngle * rollAngle;
        Eigen::Vector4d quat = q.coeffs(); // xyzw
        return quat;
    }

    /**
     * @brief 欧拉角转旋转矩阵
     * @param euler 欧拉角
     * @param degrees 是否为角度，默认值为true，表示欧拉角默认为角度值；若为false，则表示欧拉角为弧度值
     * @param sequence 欧拉角旋转顺序，默认为zyx
     * @return 旋转矩阵
     */
    Eigen::Matrix3d SpatialTransform::eulerAnglesToRotationMatrix(Eigen::Vector3d euler, bool degrees, std::string sequence) {
        Eigen::Vector4d quat = eulerAnglesToQuaternion(euler, degrees, sequence);
        Eigen::Quaterniond q(quat(3),quat(0),quat(1),quat(2));
        Eigen::Matrix3d rotMatrix = q.matrix();

        return rotMatrix;
    }

    /**
     * @brief 旋转矩阵转欧拉角
     * @param rotMatrix 旋转矩阵
     * @param degrees 是否为角度，默认值为true，表示欧拉角默认为角度值；若为false，则表示欧拉角为弧度值
     * @param sequence 欧拉角旋转顺序，默认为zyx
     * @return 欧拉角
     */
    Eigen::Vector3d SpatialTransform::rotationMatrixToEulerAngles(Eigen::Matrix3d rotMatrix, bool degrees, std::string sequence) {
        Eigen::Vector3d euler;
        // Eigen::Vector3d euler_rev;
        if(sequence == "xyz") {
            // XYZ顺序
            euler = rotMatrix.eulerAngles(0, 1, 2);
        }else if(sequence == "yzx") {
            // YZX顺序
            euler = rotMatrix.eulerAngles(1, 2, 0);
        }else if(sequence == "zxy") {
            // ZXY顺序
            euler = rotMatrix.eulerAngles(2, 0, 1);
        }else if(sequence == "xzy") {
            // XZY顺序
            euler = rotMatrix.eulerAngles(0, 2, 1);
        }else if(sequence == "yxz"){
            // YXZ顺序
            euler = rotMatrix.eulerAngles(1, 0, 2);
        }else{
            // ZYX顺序(默认)
            euler = rotMatrix.eulerAngles(2, 1, 0);
        }
        if(degrees) {
            euler = euler * 180.0 / M_PI;
        }
        // euler_rev = euler.reverse();
        // return euler_rev;
        return euler;
    }

    /**
     * @brief 四元数转旋转矩阵
     * @param quat 四元数(xyzw)
     * @return 旋转矩阵
     */
    Eigen::Matrix3d SpatialTransform::quaternionToRotationMatrix(Eigen::Vector4d quat) {
        Eigen::Quaterniond q(quat(3),quat(0),quat(1),quat(2));
        Eigen::Matrix3d rotMatrix = q.toRotationMatrix();
        return rotMatrix;
    }

    /**
     * @brief 旋转矩阵转四元数
     * @param rotMatrix 旋转矩阵
     * @return 四元数，顺序为xyzw
     */
    Eigen::Vector4d SpatialTransform::rotationMatrixToQuaternion(Eigen::Matrix3d rotMatrix) {
        Eigen::Quaterniond q(rotMatrix);
        Eigen::Vector4d quat = q.coeffs();
        return quat;
    }

    /**
     * @brief 根据dh参数和角度计算变换矩阵
     * @param d d参数
     * @param a a参数
     * @param alpha alpha参数
     * @param offset 偏移量
     * @param theta 角度
     * @return 变换矩阵
     */
    Eigen::Matrix4d SpatialTransform::transformationMatrix(double d, double a, double alpha, double offset, double theta){
        Eigen::Matrix4d transMtx;
        transMtx << cos(theta+offset), -cos(alpha)*sin(theta+offset), sin(alpha)*sin(theta+offset), a*cos(theta+offset),
                    sin(theta+offset), cos(alpha)*cos(theta+offset), -sin(alpha)*cos(theta+offset), a*sin(theta+offset),
                    0, sin(alpha), cos(alpha), d,
                    0, 0, 0, 1;
        return transMtx;
    }

    Eigen::Matrix4d SpatialTransform::getTransformationMatrix(const Eigen::Vector3d& translation, const Eigen::Vector3d& eulerAngles, std::string sequence){
        // 1. 计算旋转矩阵 (ZYX 旋转顺序)
        Eigen::Matrix3d rotationMatrix;
        rotationMatrix = eulerAnglesToRotationMatrix(eulerAngles,false,sequence);
        // 2. 构造 4×4 变换矩阵
        Eigen::Matrix4d transformationMatrix = Eigen::Matrix4d::Identity(); // 初始化单位矩阵
        transformationMatrix.block<3,3>(0,0) = rotationMatrix;  // 旋转部分
        transformationMatrix.block<3,1>(0,3) = translation;      // 平移部分
    
        return transformationMatrix;

    }
    Eigen::Matrix<double,6,1> SpatialTransform::poseTransform(Eigen::Matrix<double,6,1> ori_pose, Eigen::Matrix<double,6,1> rel_pose){
        // 机械臂末端欧拉角顺序是xyz
        Eigen::Matrix4d T1 = getTransformationMatrix(ori_pose.col(0).head(3),ori_pose.col(0).tail(3),"xyz");
        Eigen::Matrix4d T2 = getTransformationMatrix(rel_pose.col(0).head(3),rel_pose.col(0).tail(3),"xyz");

        ROS_INFO_STREAM(T1);
        ROS_INFO_STREAM(T2);

        Eigen::Matrix4d T = T1*T2;
        ROS_INFO_STREAM(T);

        // 1. 提取平移向量
        Eigen::Vector3d translation = T.block<3,1>(0,3); 
        // ROS_INFO_STREAM(translation);

        // 2. 提取旋转矩阵
        Eigen::Matrix3d rotationMatrix = T.block<3,3>(0,0);
        // 3. 计算欧拉角 (ZYX 顺序)
        Eigen::Vector3d eulerAngles = rotationMatrixToEulerAngles(rotationMatrix,false,"xyz");

        Eigen::Matrix<double,6,1>  res;
        res(0,0) = translation(0);
        res(1,0) = translation(1);
        res(2,0) = translation(2);
        res(3,0) = eulerAngles(0);
        res(4,0) = eulerAngles(1);
        res(5,0) = eulerAngles(2);

        // ROS_INFO("target_TCP_pose: [%s%.5f, %.5f, %.5f%s], [%s%.5f, %.5f, %.5f%s]",
        //     BLUE,
        //     res(0,0), res(1,0), res(2,0),
        //     RESET, CYAN,
        //     res(3,0)/M_PI*180, res(4,0)/M_PI*180, res(5,0)/M_PI*180,
        //     RESET);
        return res;
    }

    /**
     * @brief 角度转弧度
     */
    double Unit_Conv::deg2rad(double a){
        a = a*M_PI/180;
        return a;
    }
    Eigen::VectorXd Unit_Conv::deg2rad(Eigen::VectorXd vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec(i) = vec(i)*M_PI/180;
        }
        return vec;
    }
    std::vector<double> Unit_Conv::deg2rad(std::vector<double> vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec[i] = vec[i]*M_PI/180;
        }
        return vec;
    }
    /**
     * @brief 弧度转角度
     */
    double Unit_Conv::rad2deg(double a){
        a = a/M_PI*180;
        return a;
    }
    Eigen::VectorXd Unit_Conv::rad2deg(Eigen::VectorXd vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec(i) = vec(i)/M_PI*180;
        }
        return vec;
    }
    std::vector<double> Unit_Conv::rad2deg(std::vector<double> vec){
        int n = vec.size();
        for(int i = 0; i < n; i++){
            vec[i] = vec[i]/M_PI*180;
        }
        return vec;
    }

    /**
     * @brief 因时灵巧手setAngle 0-1000数值转弧度
     */
    void Unit_Conv::handAngle2Pos(std::vector<double>& vec){
        vec[0] = (1000-vec[0])/1000*1.7;
        vec[1] = (1000-vec[1])/1000*1.7;
        vec[2] = (1000-vec[2])/1000*1.7;
        vec[3] = (1000-vec[3])/1000*1.7;
        vec[4] = (1000-vec[4])/1000*0.6;
        vec[5] = (1000-vec[5])/1000*1.3;
    }
    void Unit_Conv::handAngle2Pos(Eigen::VectorXd& vec){
        vec(0) = (1000-vec(0))/1000*1.7;
        vec(1) = (1000-vec(1))/1000*1.7;
        vec(2) = (1000-vec(2))/1000*1.7;
        vec(3) = (1000-vec(3))/1000*1.7;
        vec(4) = (1000-vec(4))/1000*0.6;
        vec(5) = (1000-vec(5))/1000*1.3;
    }
    std::vector<int> Unit_Conv::handPos2Angle(std::vector<double> vec){
        std::vector<int> res(6);
        res[0] = std::round((1.7-vec[0])/1.7*1000);
        res[1] = std::round((1.7-vec[1])/1.7*1000);
        res[2] = std::round((1.7-vec[2])/1.7*1000);
        res[3] = std::round((1.7-vec[3])/1.7*1000);
        res[4] = std::round((0.6-vec[4])/0.6*1000);
        res[5] = std::round((1.3-vec[5])/1.3*1000);
        return res;
    }
    Eigen::VectorXi Unit_Conv::handPos2Angle(Eigen::VectorXd vec){
        Eigen::VectorXi res(6);
        res(0) = std::round((1.7-vec[0])/1.7*1000);
        res(1) = std::round((1.7-vec[1])/1.7*1000);
        res(2) = std::round((1.7-vec[2])/1.7*1000);
        res(3) = std::round((1.7-vec[3])/1.7*1000);
        res(4) = std::round((0.6-vec[4])/0.6*1000);
        res(5) = std::round((1.3-vec[5])/1.3*1000);
        return res;
    }

    
}