digraph G {
node [shape=box];
"world" [label="world"];
"base_1" [label="base_1"];
"base_2" [label="base_2"];
"base_3" [label="base_3"];
"base_arm" [label="base_arm"];
"l_Link1" [label="l_Link1"];
"l_Link2" [label="l_Link2"];
"l_Link3" [label="l_Link3"];
"l_Link4" [label="l_Link4"];
"l_Link4_mimic" [label="l_Link4_mimic"];
"l_Link5" [label="l_Link5"];
"l_Link6" [label="l_Link6"];
"l_Link7" [label="l_Link7"];
"l_hand_base" [label="l_hand_base"];
"l_index_MC" [label="l_index_MC"];
"l_index_PP" [label="l_index_PP"];
"l_index_MP" [label="l_index_MP"];
"l_index_DP" [label="l_index_DP"];
"l_middle_MC" [label="l_middle_MC"];
"l_middle_PP" [label="l_middle_PP"];
"l_middle_MP" [label="l_middle_MP"];
"l_middle_DP" [label="l_middle_DP"];
"l_picky_MC" [label="l_picky_MC"];
"l_picky_PP" [label="l_picky_PP"];
"l_picky_MP" [label="l_picky_MP"];
"l_picky_DP" [label="l_picky_DP"];
"l_ring_MC" [label="l_ring_MC"];
"l_ring_PP" [label="l_ring_PP"];
"l_ring_MP" [label="l_ring_MP"];
"l_ring_DP" [label="l_ring_DP"];
"l_thumb_MC" [label="l_thumb_MC"];
"l_thumb_PP" [label="l_thumb_PP"];
"l_thumb_MP" [label="l_thumb_MP"];
"l_thumb_DP" [label="l_thumb_DP"];
"r_Link1" [label="r_Link1"];
"r_Link2" [label="r_Link2"];
"r_Link3" [label="r_Link3"];
"r_Link4" [label="r_Link4"];
"r_Link4_mimic" [label="r_Link4_mimic"];
"r_Link5" [label="r_Link5"];
"r_Link6" [label="r_Link6"];
"r_Link7" [label="r_Link7"];
"r_hand_base" [label="r_hand_base"];
"r_index_MC" [label="r_index_MC"];
"r_index_PP" [label="r_index_PP"];
"r_index_MP" [label="r_index_MP"];
"r_index_DP" [label="r_index_DP"];
"r_gelsight_index" [label="r_gelsight_index"];
"r_camera_link_index" [label="r_camera_link_index"];
"r_elastomer_index" [label="r_elastomer_index"];
"r_elastomer_tip_index" [label="r_elastomer_tip_index"];
"r_middle_MC" [label="r_middle_MC"];
"r_middle_PP" [label="r_middle_PP"];
"r_middle_MP" [label="r_middle_MP"];
"r_middle_DP" [label="r_middle_DP"];
"r_gelsight_middle" [label="r_gelsight_middle"];
"r_camera_link_middle" [label="r_camera_link_middle"];
"r_elastomer_middle" [label="r_elastomer_middle"];
"r_elastomer_tip_middle" [label="r_elastomer_tip_middle"];
"r_picky_MC" [label="r_picky_MC"];
"r_picky_PP" [label="r_picky_PP"];
"r_picky_MP" [label="r_picky_MP"];
"r_picky_DP" [label="r_picky_DP"];
"r_gelsight_picky" [label="r_gelsight_picky"];
"r_camera_link_picky" [label="r_camera_link_picky"];
"r_elastomer_picky" [label="r_elastomer_picky"];
"r_elastomer_tip_picky" [label="r_elastomer_tip_picky"];
"r_ring_MC" [label="r_ring_MC"];
"r_ring_PP" [label="r_ring_PP"];
"r_ring_MP" [label="r_ring_MP"];
"r_ring_DP" [label="r_ring_DP"];
"r_gelsight_ring" [label="r_gelsight_ring"];
"r_camera_link_ring" [label="r_camera_link_ring"];
"r_elastomer_ring" [label="r_elastomer_ring"];
"r_elastomer_tip_ring" [label="r_elastomer_tip_ring"];
"r_thumb_MC" [label="r_thumb_MC"];
"r_thumb_PP" [label="r_thumb_PP"];
"r_thumb_MP" [label="r_thumb_MP"];
"r_thumb_DP" [label="r_thumb_DP"];
"r_gelsight_thumb" [label="r_gelsight_thumb"];
"r_camera_link_thumb" [label="r_camera_link_thumb"];
"r_elastomer_thumb" [label="r_elastomer_thumb"];
"r_elastomer_tip_thumb" [label="r_elastomer_tip_thumb"];
node [shape=ellipse, color=blue, fontcolor=blue];
"world" -> "world2base" [label="xyz: 0 0 0.145 \nrpy: 0 -0 0"]
"world2base" -> "base_1"
"base_1" -> "base_joint1" [label="xyz: 0 0 0 \nrpy: -1.5708 0 0"]
"base_joint1" -> "base_2"
"base_2" -> "base_joint2" [label="xyz: 0 -0.6 0 \nrpy: 0 -0 0"]
"base_joint2" -> "base_3"
"base_3" -> "base_joint3" [label="xyz: 0 -0.48 0 \nrpy: 1.5708 -0 0"]
"base_joint3" -> "base_arm"
"base_arm" -> "l_joint1" [label="xyz: 0 0.16505 0 \nrpy: 3.14 -0 0"]
"l_joint1" -> "l_Link1"
"l_Link1" -> "l_joint2" [label="xyz: 0 0 0 \nrpy: -1.57 0 0"]
"l_joint2" -> "l_Link2"
"l_Link2" -> "l_joint3" [label="xyz: 0 0 -0.1294 \nrpy: 1.57 -5.55112e-17 1.57"]
"l_joint3" -> "l_Link3"
"l_Link3" -> "l_joint4" [label="xyz: -0.357 0 0 \nrpy: -1.5708 0 0"]
"l_joint4" -> "l_Link4"
"l_Link4" -> "l_joint4_mimic" [label="xyz: -0.06 0 -0.00225 \nrpy: 0 -0 0"]
"l_joint4_mimic" -> "l_Link4_mimic"
"l_Link4_mimic" -> "l_joint5" [label="xyz: -0.2525 0 0 \nrpy: 1.5708 -0 0"]
"l_joint5" -> "l_Link5"
"l_Link5" -> "l_joint6" [label="xyz: 0 0 0 \nrpy: -1.5708 0 0"]
"l_joint6" -> "l_Link6"
"l_Link6" -> "l_joint7" [label="xyz: -0.1385 0 0 \nrpy: 0 1.5708 -3.1416"]
"l_joint7" -> "l_Link7"
"l_Link7" -> "l_end_effector" [label="xyz: 0.0015 0 0 \nrpy: 0 0 -1.57"]
"l_end_effector" -> "l_hand_base"
"l_hand_base" -> "l_index_MCP_FE" [label="xyz: -0.032722 0.0069265 0.11045 \nrpy: 0 1.5708 -1.5708"]
"l_index_MCP_FE" -> "l_index_MC"
"l_index_MC" -> "l_index_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"l_index_MCP_AA" -> "l_index_PP"
"l_index_PP" -> "l_index_PIP" [label="xyz: -0.044075 -0.00275 0 \nrpy: 0 -0 0"]
"l_index_PIP" -> "l_index_MP"
"l_index_MP" -> "l_index_DIP" [label="xyz: -0.025098 0.00275 0.0050003 \nrpy: 0 -0 0"]
"l_index_DIP" -> "l_index_DP"
"l_hand_base" -> "l_middle_MCP_FE" [label="xyz: -0.010322 0.0069265 0.11945 \nrpy: 0 1.5708 -1.5708"]
"l_middle_MCP_FE" -> "l_middle_MC"
"l_middle_MC" -> "l_middle_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"l_middle_MCP_AA" -> "l_middle_PP"
"l_middle_PP" -> "l_middle_PIP" [label="xyz: -0.044075 -0.00275 0 \nrpy: 0 -0 0"]
"l_middle_PIP" -> "l_middle_MP"
"l_middle_MP" -> "l_middle_DIP" [label="xyz: -0.025098 0.00275 0.0050003 \nrpy: 0 -0 0"]
"l_middle_DIP" -> "l_middle_DP"
"l_hand_base" -> "l_picky_MCP_FE" [label="xyz: 0.034478 0.0069265 0.10145 \nrpy: 0 1.5708 -1.5708"]
"l_picky_MCP_FE" -> "l_picky_MC"
"l_picky_MC" -> "l_picky_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"l_picky_MCP_AA" -> "l_picky_PP"
"l_picky_PP" -> "l_picky_PIP" [label="xyz: -0.044075 -0.00275 0 \nrpy: 0 -0 0"]
"l_picky_PIP" -> "l_picky_MP"
"l_picky_MP" -> "l_picky_DIP" [label="xyz: -0.025098 0.00275 0.0050003 \nrpy: 0 -0 0"]
"l_picky_DIP" -> "l_picky_DP"
"l_hand_base" -> "l_ring_MCP_FE" [label="xyz: 0.012078 0.0069265 0.11045 \nrpy: 0 1.5708 -1.5708"]
"l_ring_MCP_FE" -> "l_ring_MC"
"l_ring_MC" -> "l_ring_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"l_ring_MCP_AA" -> "l_ring_PP"
"l_ring_PP" -> "l_ring_PIP" [label="xyz: -0.044075 -0.00275 0 \nrpy: 0 -0 0"]
"l_ring_PIP" -> "l_ring_MP"
"l_ring_MP" -> "l_ring_DIP" [label="xyz: -0.025098 0.00275 0.0050003 \nrpy: 0 -0 0"]
"l_ring_DIP" -> "l_ring_DP"
"l_hand_base" -> "l_thumb_MCP_FE" [label="xyz: -0.032422 -0.017073 0.040951 \nrpy: 0 1.5708 -0.00384279"]
"l_thumb_MCP_FE" -> "l_thumb_MC"
"l_thumb_MC" -> "l_thumb_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"l_thumb_MCP_AA" -> "l_thumb_PP"
"l_thumb_PP" -> "l_thumb_PIP" [label="xyz: -0.031796 -0.038811 0.0024025 \nrpy: -0.87266 -5.55112e-17 0.69813"]
"l_thumb_PIP" -> "l_thumb_MP"
"l_thumb_MP" -> "l_thumb_DIP" [label="xyz: -0.025096 0.00315 0.005 \nrpy: 0 -0 0"]
"l_thumb_DIP" -> "l_thumb_DP"
"base_arm" -> "r_joint1" [label="xyz: 0 -0.16495 0 \nrpy: 0 -0 0"]
"r_joint1" -> "r_Link1"
"r_Link1" -> "r_joint2" [label="xyz: 0 0 0 \nrpy: 1.5708 -0 0"]
"r_joint2" -> "r_Link2"
"r_Link2" -> "r_joint3" [label="xyz: 0 0 0.1295 \nrpy: -1.5708 -0 1.5708"]
"r_joint3" -> "r_Link3"
"r_Link3" -> "r_joint4" [label="xyz: -0.357 0 0 \nrpy: 1.5708 -0 0"]
"r_joint4" -> "r_Link4"
"r_Link4" -> "r_joint4_mimic" [label="xyz: -0.06 0 0.00225 \nrpy: 0 -0 0"]
"r_joint4_mimic" -> "r_Link4_mimic"
"r_Link4_mimic" -> "r_joint5" [label="xyz: -0.2525 0 0 \nrpy: -1.5708 0 0"]
"r_joint5" -> "r_Link5"
"r_Link5" -> "r_joint6" [label="xyz: 0 0 0 \nrpy: 1.5708 -0 0"]
"r_joint6" -> "r_Link6"
"r_Link6" -> "r_joint7" [label="xyz: -0.134 0 0 \nrpy: 1.57 -5.55112e-17 1.57"]
"r_joint7" -> "r_Link7"
"r_Link7" -> "r_end_effector" [label="xyz: 0 0 0 \nrpy: 0 -1.5708 1.5708"]
"r_end_effector" -> "r_hand_base"
"r_hand_base" -> "r_index_MCP_FE" [label="xyz: -0.11526 0.033061 -0.0079018 \nrpy: 0 -0 0"]
"r_index_MCP_FE" -> "r_index_MC"
"r_index_MC" -> "r_index_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_index_MCP_AA" -> "r_index_PP"
"r_index_PP" -> "r_index_PIP" [label="xyz: -0.044075 0 0 \nrpy: 0 -0 0"]
"r_index_PIP" -> "r_index_MP"
"r_index_MP" -> "r_index_DIP" [label="xyz: -0.025098 0 0.0050003 \nrpy: 0 -0 0"]
"r_index_DIP" -> "r_index_DP"
"r_index_DP" -> "r_index_TIP_gelsight" [label="xyz: 0 -0.001765 0.00175 \nrpy: -3.14159 -0 -3.14159"]
"r_index_TIP_gelsight" -> "r_gelsight_index"
"r_gelsight_index" -> "r_index_TIP_camera" [label="xyz: 0.0036365 0.0017649 0.0084125 \nrpy: 3.14159 0.95481 3.14159"]
"r_index_TIP_camera" -> "r_camera_link_index"
"r_gelsight_index" -> "r_index_TIP_elastomer" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_index_TIP_elastomer" -> "r_elastomer_index"
"r_gelsight_index" -> "r_index_TIP_elastomer_tip" [label="xyz: 0.016231 0.0017649 -0.00010532 \nrpy: 3.14159 0.37247 3.14159"]
"r_index_TIP_elastomer_tip" -> "r_elastomer_tip_index"
"r_hand_base" -> "r_middle_MCP_FE" [label="xyz: -0.12426 0.010661 -0.0079018 \nrpy: 0 -0 0"]
"r_middle_MCP_FE" -> "r_middle_MC"
"r_middle_MC" -> "r_middle_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_middle_MCP_AA" -> "r_middle_PP"
"r_middle_PP" -> "r_middle_PIP" [label="xyz: -0.044075 0 0 \nrpy: 0 -0 0"]
"r_middle_PIP" -> "r_middle_MP"
"r_middle_MP" -> "r_middle_DIP" [label="xyz: -0.025098 0 0.0050003 \nrpy: 0 -0 0"]
"r_middle_DIP" -> "r_middle_DP"
"r_middle_DP" -> "r_middle_TIP_gelsight" [label="xyz: 0 -0.001765 0.00175 \nrpy: -3.14159 -0 -3.14159"]
"r_middle_TIP_gelsight" -> "r_gelsight_middle"
"r_gelsight_middle" -> "r_middle_TIP_camera" [label="xyz: 0.0036365 0.0017649 0.0084125 \nrpy: 3.14159 0.95481 3.14159"]
"r_middle_TIP_camera" -> "r_camera_link_middle"
"r_gelsight_middle" -> "r_middle_TIP_elastomer" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_middle_TIP_elastomer" -> "r_elastomer_middle"
"r_gelsight_middle" -> "r_middle_TIP_elastomer_tip" [label="xyz: 0.016231 0.0017649 -0.00010532 \nrpy: 3.14159 0.37247 3.14159"]
"r_middle_TIP_elastomer_tip" -> "r_elastomer_tip_middle"
"r_hand_base" -> "r_picky_MCP_FE" [label="xyz: -0.10626 -0.034139 -0.0079018 \nrpy: 0 -0 0"]
"r_picky_MCP_FE" -> "r_picky_MC"
"r_picky_MC" -> "r_picky_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_picky_MCP_AA" -> "r_picky_PP"
"r_picky_PP" -> "r_picky_PIP" [label="xyz: -0.044075 0 0 \nrpy: 0 -0 0"]
"r_picky_PIP" -> "r_picky_MP"
"r_picky_MP" -> "r_picky_DIP" [label="xyz: -0.025098 0 0.0050003 \nrpy: 0 -0 0"]
"r_picky_DIP" -> "r_picky_DP"
"r_picky_DP" -> "r_picky_TIP_gelsight" [label="xyz: 0 -0.001765 0.00175 \nrpy: 3.14159 -0 3.14159"]
"r_picky_TIP_gelsight" -> "r_gelsight_picky"
"r_gelsight_picky" -> "r_picky_TIP_camera" [label="xyz: 0.0036365 0.0017649 0.0084125 \nrpy: 3.14159 0.95481 3.14159"]
"r_picky_TIP_camera" -> "r_camera_link_picky"
"r_gelsight_picky" -> "r_picky_TIP_elastomer" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_picky_TIP_elastomer" -> "r_elastomer_picky"
"r_gelsight_picky" -> "r_picky_TIP_elastomer_tip" [label="xyz: 0.016231 0.0017649 -0.00010532 \nrpy: 3.14159 0.37247 3.14159"]
"r_picky_TIP_elastomer_tip" -> "r_elastomer_tip_picky"
"r_hand_base" -> "r_ring_MCP_FE" [label="xyz: -0.11526 -0.011739 -0.0079018 \nrpy: 0 -0 0"]
"r_ring_MCP_FE" -> "r_ring_MC"
"r_ring_MC" -> "r_ring_MCP_AA" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_ring_MCP_AA" -> "r_ring_PP"
"r_ring_PP" -> "r_ring_PIP" [label="xyz: -0.044075 0 0 \nrpy: 0 -0 0"]
"r_ring_PIP" -> "r_ring_MP"
"r_ring_MP" -> "r_ring_DIP" [label="xyz: -0.025098 0 0.0050003 \nrpy: 0 -0 0"]
"r_ring_DIP" -> "r_ring_DP"
"r_ring_DP" -> "r_ring_TIP_gelsight" [label="xyz: 0 -0.001765 0.00175 \nrpy: -3.14159 -0 -3.14159"]
"r_ring_TIP_gelsight" -> "r_gelsight_ring"
"r_gelsight_ring" -> "r_ring_TIP_camera" [label="xyz: 0.0036365 0.0017649 0.0084125 \nrpy: 3.14159 0.95481 3.14159"]
"r_ring_TIP_camera" -> "r_camera_link_ring"
"r_gelsight_ring" -> "r_ring_TIP_elastomer" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_ring_TIP_elastomer" -> "r_elastomer_ring"
"r_gelsight_ring" -> "r_ring_TIP_elastomer_tip" [label="xyz: 0.016231 0.0017649 -0.00010532 \nrpy: 3.14159 0.37247 3.14159"]
"r_ring_TIP_elastomer_tip" -> "r_elastomer_tip_ring"
"r_hand_base" -> "r_thumb_MCP_FE" [label="xyz: -0.04576 0.032759 0.016098 \nrpy: 0 -0 0"]
"r_thumb_MCP_FE" -> "r_thumb_MC"
"r_thumb_MC" -> "r_thumb_MCP_AA" [label="xyz: 0 0 0 \nrpy: -1.3963 0 0"]
"r_thumb_MCP_AA" -> "r_thumb_PP"
"r_thumb_PP" -> "r_thumb_PIP" [label="xyz: -0.033091 0 0.037267 \nrpy: 2.4435 0.69813 0"]
"r_thumb_PIP" -> "r_thumb_MP"
"r_thumb_MP" -> "r_thumb_DIP" [label="xyz: -0.025096 0 0.005 \nrpy: 0 -0 0"]
"r_thumb_DIP" -> "r_thumb_DP"
"r_thumb_DP" -> "r_thumb_TIP_gelsight" [label="xyz: 0 -0.001765 0.00175 \nrpy: 3.14159 -0 3.14159"]
"r_thumb_TIP_gelsight" -> "r_gelsight_thumb"
"r_gelsight_thumb" -> "r_thumb_TIP_camera" [label="xyz: 0.0040417 0.001765 0.0081471 \nrpy: -3.14159 0.95993 -3.14159"]
"r_thumb_TIP_camera" -> "r_camera_link_thumb"
"r_gelsight_thumb" -> "r_thumb_TIP_elastomer" [label="xyz: 0 0 0 \nrpy: 0 -0 0"]
"r_thumb_TIP_elastomer" -> "r_elastomer_thumb"
"r_gelsight_thumb" -> "r_thumb_TIP_elastomer_tip" [label="xyz: 0.015506 0.0017822 -0.00037374 \nrpy: -3.14159 0.36356 -3.14159"]
"r_thumb_TIP_elastomer_tip" -> "r_elastomer_tip_thumb"
}
