<?xml version="1.0" encoding="utf-8"?>
<!-- This URDF was automatically created by SolidWorks to URDF Exporter! Originally created by <PERSON> (<EMAIL>) 
     Commit Version: 1.6.0-4-g7f85cfe  Build Version: 1.6.7995.38578
     For more information, please see http://wiki.ros.org/sw_urdf_exporter -->
<robot
  name="robot_arm">

  <link name="world"/>

  <link
    name="base_0">
    <inertial>
      <origin
        xyz="0.000631634049925667 0.00624319931148147 -0.645774146992794"
        rpy="0 0 0" />
      <mass
        value="6.85288624152674" />
      <inertia
        ixx="0.0529979828784803"
        ixy="1.71662089677197E-07"
        ixz="8.68253528798124E-08"
        iyy="0.039831920310509"
        iyz="-0.00215571118826311"
        izz="0.0454043069698164" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/base_0.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/base_0.STL" />
      </geometry>
    </collision>
  </link>
  <joint name="world2base" type="fixed">
        <parent link="world" />
        <child link="base_0"/>
        <origin xyz="0 0 1.571" rpy="0 0 0"/>
  </joint>
  <link
    name="l_Link1">
    <inertial>
      <origin
        xyz="6.31445167355027E-05 -0.000887062704731878 -3.15110131210439E-05"
        rpy="0 0 0" />
      <mass
        value="0.436403784733489" />
      <inertia
        ixx="0.000304981735988441"
        ixy="-2.65454404984865E-12"
        ixz="-8.56215025516638E-08"
        iyy="0.000298813758622503"
        iyz="-3.19657194657039E-07"
        izz="0.000446290979573794" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint1"
    type="continuous">
    <origin
      xyz="0 0.16505 0"
      rpy="0 0 0" />
    <parent
      link="base_0" />
    <child
      link="l_Link1" />
    <axis
      xyz="0 0 -1" />
  </joint>
  <link
    name="l_Link2">
    <inertial>
      <origin
        xyz="-0.00548731670358221 3.68320164048086E-05 0.101178720910148"
        rpy="0 0 0" />
      <mass
        value="0.585126701861527" />
      <inertia
        ixx="0.000653609977994595"
        ixy="-5.94192878114858E-07"
        ixz="1.87408981422657E-05"
        iyy="0.000552218961920917"
        iyz="-6.06730800625592E-07"
        izz="0.00054746322567633" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint2"
    type="continuous">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="l_Link1" />
    <child
      link="l_Link2" />
    <axis
      xyz="0 0 -1" />
  </joint>
  <link
    name="l_Link3">
    <inertial>
      <origin
        xyz="-0.208209353747596 -0.000661214294693457 0.000149105610168676"
        rpy="0 0 0" />
      <mass
        value="0.5050589568918" />
      <inertia
        ixx="0.000216923846588863"
        ixy="-2.1073075702318E-05"
        ixz="1.2844021311197E-05"
        iyy="0.00120575127811288"
        iyz="-5.96476693868772E-06"
        izz="0.00111303075718569" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint3"
    type="continuous">
    <origin
      xyz="0 0 0.1294"
      rpy="-1.5708 0 -1.5708" />
    <parent
      link="l_Link2" />
    <child
      link="l_Link3" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="l_Link4">
    <inertial>
      <origin
        xyz="-0.0281665464550487 -1.66533453693773E-16 -9.35944287158152E-05"
        rpy="0 0 0" />
      <mass
        value="0.0199426877687591" />
      <inertia
        ixx="2.96538352859809E-06"
        ixy="3.69019057135681E-21"
        ixz="-1.86675439967539E-07"
        iyy="1.8765775426067E-05"
        iyz="-8.07956487484396E-22"
        izz="2.15819605045628E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint4"
    type="continuous">
    <origin
      xyz="-0.357 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="l_Link3" />
    <child
      link="l_Link4" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="l_Link5">
    <inertial>
      <origin
        xyz="-0.0752004725328291 -0.000810778625682851 -1.30612599973934E-05"
        rpy="0 0 0" />
      <mass
        value="0.242656425906805" />
      <inertia
        ixx="6.62209332631248E-05"
        ixy="2.45810100872632E-08"
        ixz="-5.47314952673242E-09"
        iyy="0.000356454991180688"
        iyz="1.52864962520155E-09"
        izz="0.000388234421821599" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint5"
    type="continuous">
    <origin
      xyz="-0.06 0 -0.00225"
      rpy="0 0 0" />
    <parent
      link="l_Link4" />
    <child
      link="l_Link5" />
    <axis
      xyz="0 0 -1" />
    <mimic
      joint="l_joint4"
      multiplier="-1"
      offset="0" />
  </joint>
  <link
    name="l_Link6">
    <inertial>
      <origin
        xyz="2.47592351863557E-05 -4.74781231873944E-06 -0.00229471749597721"
        rpy="0 0 0" />
      <mass
        value="0.0372662426645848" />
      <inertia
        ixx="4.2427624945891E-06"
        ixy="3.06377692898111E-11"
        ixz="1.14548420290991E-13"
        iyy="3.95071590836367E-06"
        iyz="2.75908402032644E-13"
        izz="7.1234820941962E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint6"
    type="continuous">
    <origin
      xyz="-0.2525 0 0"
      rpy="1.5708 0 0" />
    <parent
      link="l_Link5" />
    <child
      link="l_Link6" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="l_Link7">
    <inertial>
      <origin
        xyz="-0.0389356549006473 1.05351387325037E-05 0.000816035681464045"
        rpy="0 0 0" />
      <mass
        value="0.0980000337957391" />
      <inertia
        ixx="2.58823491197887E-05"
        ixy="2.98957026700164E-09"
        ixz="1.00860392852914E-08"
        iyy="3.55841578211862E-05"
        iyz="-2.93398232365074E-08"
        izz="3.99613629951895E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link7.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint7"
    type="continuous">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="l_Link6" />
    <child
      link="l_Link7" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="l_Link8">
    <inertial>
      <origin
        xyz="0.000130937451066404 0.00011030487527447 0.109245285006582"
        rpy="0 0 0" />
      <mass
        value="0.0824682885646626" />
      <inertia
        ixx="2.15183389267187E-05"
        ixy="2.65224796771892E-08"
        ixz="-4.67570011410033E-08"
        iyy="2.17644828235974E-05"
        iyz="-3.48895233550039E-09"
        izz="3.5644596609635E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link8.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/l_Link8.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="l_joint8"
    type="continuous">
    <origin
      xyz="0 0 0"
      rpy="3.1416 1.5708 0" />
    <parent
      link="l_Link7" />
    <child
      link="l_Link8" />
    <axis
      xyz="0 0 1" />
  </joint>



  <link name="hand_l"/>
  <joint
    name="end_effector_l"
    type="fixed">
    <origin
      xyz="0 0 0.1"
      rpy="0 0 0" />
    <parent
      link="l_Link8" />
    <child
      link="hand_l" />
  </joint>

  <link
    name="r_Link1">
    <inertial>
      <origin
        xyz="6.76867879201848E-05 -0.000903931135904062 -3.56608652785084E-05"
        rpy="0 0 0" />
      <mass
        value="0.431056457943263" />
      <inertia
        ixx="0.000302507987001375"
        ixy="-2.65452087985145E-12"
        ixz="-8.56215025520346E-08"
        iyy="0.000293870632390727"
        iyz="-3.19657194658115E-07"
        izz="0.000443817230586721" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link1.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link1.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint1"
    type="continuous">
    <origin
      xyz="0 -0.16495 0"
      rpy="0 0 0" />
    <parent
      link="base_0" />
    <child
      link="r_Link1" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="r_Link2">
    <inertial>
      <origin
        xyz="0.00543828327872348 3.65030081852513E-05 -0.100373714270726"
        rpy="0 0 0" />
      <mass
        value="0.590402422555339" />
      <inertia
        ixx="0.000656083218952701"
        ixy="5.94191053830925E-07"
        ixz="1.87408981544293E-05"
        iyy="0.000554692204253716"
        iyz="6.06730816304211E-07"
        izz="0.000552406206489923" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link2.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link2.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint2"
    type="continuous">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="r_Link1" />
    <child
      link="r_Link2" />
    <axis
      xyz="0 0 -1" />
  </joint>
  <link
    name="r_Link3">
    <inertial>
      <origin
        xyz="-0.2082093537512 -0.000661214297151047 0.000149105614352996"
        rpy="0 0 0" />
      <mass
        value="0.505058956903164" />
      <inertia
        ixx="0.000216923846591366"
        ixy="-2.10730757014554E-05"
        ixz="1.2844021309649E-05"
        iyy="0.00120575127812064"
        iyz="-5.96476694296649E-06"
        izz="0.00111303075719106" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link3.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link3.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint3"
    type="continuous">
    <origin
      xyz="0 0 -0.1295"
      rpy="1.5708 0 -1.5708" />
    <parent
      link="r_Link2" />
    <child
      link="r_Link3" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="r_Link4">
    <inertial>
      <origin
        xyz="-0.0281665464550495 5.55111512312578E-17 9.35944287152046E-05"
        rpy="0 0 0" />
      <mass
        value="0.0199426877687593" />
      <inertia
        ixx="2.96538352859811E-06"
        ixy="1.30006772561408E-20"
        ixz="1.86675439967549E-07"
        iyy="1.87657754260671E-05"
        iyz="5.47684483997695E-22"
        izz="2.1581960504563E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link4.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link4.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint4"
    type="continuous">
    <origin
      xyz="-0.357 0 0"
      rpy="1.5708 0 0" />
    <parent
      link="r_Link3" />
    <child
      link="r_Link4" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="r_Link5">
    <inertial>
      <origin
        xyz="-0.0750923237562541 -0.000831115580014752 -6.65894588730565E-06"
        rpy="0 0 0" />
      <mass
        value="0.242501585243888" />
      <inertia
        ixx="6.62204329953082E-05"
        ixy="2.45785261026671E-08"
        ixz="5.47314952678278E-09"
        iyy="0.000356454753003177"
        iyz="1.52864962522983E-09"
        izz="0.000388233921553783" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link5.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link5.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint5"
    type="continuous">
    <origin
      xyz="-0.06 0 0.00225"
      rpy="0 0 0" />
    <parent
      link="r_Link4" />
    <child
      link="r_Link5" />
    <axis
      xyz="0 0 1" />
    <mimic
      joint="r_joint4"
      multiplier="1"
      offset="0" />
  </joint>
  <link
    name="r_Link6">
    <inertial>
      <origin
        xyz="-2.47592351883541E-05 4.74781231812882E-06 -0.00229471749597909"
        rpy="0 0 0" />
      <mass
        value="0.0372662426645848" />
      <inertia
        ixx="4.24276249458909E-06"
        ixy="3.06377692909288E-11"
        ixz="-1.14548063223233E-13"
        iyy="3.95071590836366E-06"
        iyz="-2.75908404532117E-13"
        izz="7.12348209419618E-06" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link6.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link6.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint6"
    type="continuous">
    <origin
      xyz="-0.2525 0 0"
      rpy="1.5708 0 0" />
    <parent
      link="r_Link5" />
    <child
      link="r_Link6" />
    <axis
      xyz="0 0 -1" />
  </joint>
  <link
    name="r_Link7">
    <inertial>
      <origin
        xyz="-0.0389178609640028 1.9692669638971E-05 0.000816350189191239"
        rpy="0 0 0" />
      <mass
        value="0.097962278223917" />
      <inertia
        ixx="2.58822870387763E-05"
        ixy="2.9895702670181E-09"
        ixz="1.00860392852913E-08"
        iyy="3.55839964031528E-05"
        iyz="-2.93398232364859E-08"
        izz="3.99612015771559E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link7.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link7.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint7"
    type="continuous">
    <origin
      xyz="0 0 0"
      rpy="-1.5708 0 0" />
    <parent
      link="r_Link6" />
    <child
      link="r_Link7" />
    <axis
      xyz="0 0 1" />
  </joint>
  <link
    name="r_Link8">
    <inertial>
      <origin
        xyz="0.000130937451068125 0.000110304875289347 0.109245285006611"
        rpy="0 0 0" />
      <mass
        value="0.0824682885646653" />
      <inertia
        ixx="2.15183389267196E-05"
        ixy="2.65224796769152E-08"
        ixz="-4.67570011410225E-08"
        iyy="2.17644828235986E-05"
        iyz="-3.48895233429207E-09"
        izz="3.56445966096362E-05" />
    </inertial>
    <visual>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link8.STL" />
      </geometry>
      <material
        name="">
        <color
          rgba="1 1 1 1" />
      </material>
    </visual>
    <collision>
      <origin
        xyz="0 0 0"
        rpy="0 0 0" />
      <geometry>
        <mesh
          filename="package://robot_arm/meshes/r_Link8.STL" />
      </geometry>
    </collision>
  </link>
  <joint
    name="r_joint8"
    type="continuous">
    <origin
      xyz="0 0 0"
      rpy="3.1416 1.5708 0" />
    <parent
      link="r_Link7" />
    <child
      link="r_Link8" />
    <axis
      xyz="0 0 -1" />
  </joint>

  <link name="hand_r"/>
  <joint
    name="end_effector_r"
    type="fixed">
    <origin
      xyz="0 0 0.1995"
      rpy="0 0 0" />
    <parent
      link="r_Link8" />
    <child
      link="hand_r" />
    <axis
      xyz="0 0 1" />
  </joint>
  
</robot>