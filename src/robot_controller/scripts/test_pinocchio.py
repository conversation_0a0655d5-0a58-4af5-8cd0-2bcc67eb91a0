from pathlib import Path
import pinocchio
import numpy as np

print(f"当前使用的 Pinocchio 版本: {pinocchio.__version__}")

pinocchio_model_dir = Path(__file__).resolve().parent.parent / "description/urdf"
model_path = pinocchio_model_dir / "S1_robot.urdf"
print(model_path)
model_full = pinocchio.buildModelFromUrdf(str(model_path))
model_mimic_from_urdf = pinocchio.buildModelFromUrdf(str(model_path), mimic=True)

print(f"{model_full.nq=}")
print(f"{model_mimic_from_urdf.nq=}")