/**
 * @file relaxed_ik_wrapper.h
 * @brief C++ wrapper header for the relaxed_ik_lib Rust library
 *
 * This file provides a C++ interface to the relaxed_ik_lib Rust library,
 * implementing the same functionality as the python_wrapper.py file.
 */

#ifndef RELAXED_IK_WRAPPER_H
#define RELAXED_IK_WRAPPER_H

#include <vector>
#include <string>
#include <memory>
#include <iostream>

/**
 * @brief C++ wrapper for the relaxed_ik_lib Rust library
 *
 * This class provides a C++ interface to the relaxed_ik_lib Rust library,
 * implementing the same functionality as the RelaxedIKRust class in python_wrapper.py.
 */
class RelaxedIKRust {
private:
    // Forward declaration of implementation class (Pimpl idiom)
    class Impl;

    // Pointer to implementation
    std::unique_ptr<Impl> pimpl;

public:
    /**
     * @brief Constructor for RelaxedIKRust
     *
     * @param setting_file_path Path to the setting file. If empty, the default setting file will be used.
     */
    RelaxedIKRust(const std::string& setting_file_path = "");

    /**
     * @brief Destructor for RelaxedIKRust
     */
    ~RelaxedIKRust();

    /**
     * @brief 初始化RelaxedIK求解器并返回求解器对象
     *
     * @param setting_file_path 设置文件路径，默认为空字符串
     * @return RelaxedIKRust* 指向初始化好的RelaxedIK求解器的指针，如果初始化失败则返回nullptr
     */
    static RelaxedIKRust* initialize(const std::string& setting_file_path = "");

    /**
     * @brief Solve the inverse kinematics for position
     *
     * Assuming the robot has N end-effectors:
     * @param positions Vector of end-effector positions (3*N elements)
     * @param orientations Vector of end-effector orientations in quaternion xyzw format (4*N elements)
     * @param tolerances Vector of tolerances for each end-effector (6*N elements)
     * @return std::vector<double> Joint angles solution
     */
    std::vector<double> solve_position(
        const std::vector<double>& positions,
        const std::vector<double>& orientations,
        const std::vector<double>& tolerances
    );

    /**
     * @brief Solve the inverse kinematics for velocity
     *
     * Assuming the robot has N end-effectors:
     * @param linear_velocities Vector of end-effector linear velocities (3*N elements)
     * @param angular_velocities Vector of end-effector angular velocities (4*N elements)
     * @param tolerances Vector of tolerances for each end-effector (6*N elements)
     * @return std::vector<double> Joint velocities solution
     */
    std::vector<double> solve_velocity(
        const std::vector<double>& linear_velocities,
        const std::vector<double>& angular_velocities,
        const std::vector<double>& tolerances
    );

    /**
     * @brief Reset the solver with a new joint state
     *
     * @param joint_state Vector of joint angles
     */
    void reset_solver(const std::vector<double>& joint_state);
};



#endif // RELAXED_IK_WRAPPER_H
