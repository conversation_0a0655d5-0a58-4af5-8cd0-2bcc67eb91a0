#ifndef PLAN_H
#define PLAN_H

#include <iostream>
#include <Eigen/Dense>
#include <Eigen/Geometry>
#include <Eigen/Core>
#include <math.h>
#include <kdl/kdl.hpp> 
#include <kdl/chain.hpp> 
#include <kdl/tree.hpp> 
#include <kdl/segment.hpp> 
#include <kdl/chainfksolver.hpp> 
#include <kdl_parser/kdl_parser.hpp> 
#include <kdl/chainfksolverpos_recursive.hpp> 
#include <kdl/frames_io.hpp> 
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainjnttojacsolver.hpp>
#include <kdl/chainiksolver.hpp>
#include <tf/transform_broadcaster.h>
#include <kdl/chainiksolvervel_pinv.hpp>
#include <eigen_conversions/eigen_kdl.h>
#include "plan.h"
#include <string>
#include "ros/ros.h"
#include <std_msgs/Float64.h>
#include <sensor_msgs/JointState.h>
#include <std_msgs/Float64MultiArray.h>
#include <vector>

// #define PI		3.14159265358979323846	
#define Rad2Deg        180.0/3.1415926535897932384 
#define Deg2Rad        3.14159265358979323846/180.0
#define JNT_NUM_L 8

using namespace Eigen;
using namespace std;
using namespace KDL;


typedef Matrix<float, 8, 1>  Vector8f;
typedef Matrix<float, 7, 1>  Vector7f;
typedef Matrix<float, 14, 1>  Vector14f;
typedef Matrix<float, 16, 1>  Vector16f;


typedef Eigen::Matrix4f Matrix4;          // 4x4变换矩;

vector<int> angleToMotor(const std::vector<double>& radian_angles);

void Interp3rdPoly_Param_P2P_Zrros_fcn(float q0, float qf, float tf, float a0345[4]);

void Interp3rdPoly_Data_t_P2P_Zrros_fcn(float tt, float a0345[4], float qq_data[3]);

void MultipleJointP2PPlan(float Tx,int JNT_NUM, float tf, float Angle0[], float Angled[], float fDesiredJntAngleDeg[], float fDesiredJntRateDeg[], float fDesiredJntAccDeg[]);

void MultipleJointCartPlan(float Tx,float tf,float init_pos[], float final_pos[], float DesiredPos[],float DesiredVel[],float DesiredAcc[]);

void MoveJ(double speed,float start[], float target[]);

void MoveL(float Tx, float tf,float start[], float target[],float DesiredPos[]);

void MoveC(double speed, Eigen::Matrix4f start, Eigen::Matrix4f aux, Eigen::Matrix4f target);

void trapezoidal_plan(int JNT_NUM, float init_pos[], float final_pos[], double max_vel, double max_acc,float fDesiredJntAngleDeg[]);

void Rbt_EulerXyz2Tr(float Euler_xyz[], float TransMtrx[][4]);

struct SShapeTrajectory{
    double p0, pf;    // 起始和终止位置
    double v0, vf;    // 起始和终止速度
    double a0, af;    // 起始和终止加速度
    double T;         // 总时间
    double coeffs[6];   // 多项式系数数组

    SShapeTrajectory(){
        p0 = 0.0;
        pf = 0.0;
        v0 = 0.0;
        vf = 0.0;
        a0 = 0.0;
        af = 0.0;
        T = 0.0;
        memset(coeffs,0,sizeof(coeffs));
        }
    // memset(this,0,sizeof(sshapetrajectory));
};

    void calculateCoefficients(SShapeTrajectory *traj);
    void quinticPolynomialsCalculate(SShapeTrajectory *traj, double t,double output[3]);
    void quinticPolynomialsPlanner(SShapeTrajectory *traj,float Tx,int JNT_NUM,float tf,float Angle0[], float Angled[],float fDesiredJntAngleDeg[], float fDesiredJntRateDeg[], float fDesiredJntAccDeg[]);

    // 结构体用于存储三次样条的系数
    typedef struct 
    {
        double a, b, c, d;
    } Spline;
   void CubicSplineCoefficients(Spline* Splines,VectorXd x, VectorXd y);
   void SplineEval(Spline* Splines, VectorXd x, double x_val,double* output);
   void CubicSplinePlanner(Spline* Splines,float Tx,int JNT_NUM,VectorXd tf,MatrixXd Joint_Desired, VectorXd& Joint_Seg);

    void SplineCal(VectorXd x, VectorXd y,double x_val,double* output);
    void SplinePlanner(float Tx,int JNT_NUM,VectorXd tf,MatrixXd Joint_Desired, VectorXd &Joint_Seg);

typedef enum ctrl_mode{
    stop = 0,
    rviz_ctrl,
    gazebo_ctrl,
    robot_ctrl
}CTRL_MODE;
class robot_control
{
private:
    /* data */
    // ros::NodeHandle nh;
    Chain chain;
    ros::Publisher pos_pub;
    ros::Subscriber joint_sub;
    Frame cartpos;
    JntArray jointpos;
    float left_arm_pos[8] = {0.0};
    float right_arm_pos[8] = {0.0};
    bool jntSubFlag = false;
    //ros系统时间
	ros::Time time_current, time_prev;
    sensor_msgs::JointState msg;
    sensor_msgs::JointState msg_pre;
    float DH_OBC[8][4];
    //q_offset,d,a,alpha
    float dh_param_l[8][4] ={{0,0,0, -PI/2},
                          {-PI/2,0.1295f,0, -PI/2},
                          {0,0,-0.357f,-PI/2},
                          {0,0,-0.06f,0},
                          {0,0,-0.2525f,0},
                          {0,0,0, PI/2},
                          {-PI/2,0,0, PI/2},
                          {0,0.1995,0,0}};

    float dh_param_r[8][4] ={{0,0,0, -PI/2},
                        {-PI/2,-0.1295f,0, PI/2},
                        {0,0,-0.357f,PI/2},
                        {0,0,-0.06f,0},
                        {0,0,-0.2525f,0},
                        {0,0,0, PI/2},
                        {-PI/2,0,0, PI/2},
                        {0,0.1995,0,0}};

public:
    robot_control(const char* urdf_path,const char* arm_type);
    robot_control(const char* arm_type);
    int Rbt_InvMtrx(float* C, float* IC, int n);
    void Rbt_MulMtrx(int m, int n, int p, float* A, float* B, float* C);
    void nfCross(float u[], float v[], float n[]);
    void Rbt_CalJcb(float DH_OBC[][4], float JointAngle8[], float dRbtJcb[][8], float T0n_c[][4]);
    void Rbt_PInvMtrx67(float AA[][7], float AA_pinv[][6]);
    int nfInvKinePosLev(float T0n[][4], float JntCurrent8[], float JntCalcted8[]);
    void nfFkineSpaceRbt(float JntCurrent8[], float T0n[][4]);
    Matrix4 dhTransform(float q, float alpha, float a, float d);
    Matrix4 forwardKinematics(const VectorXf q);
    void pub2Rviz(ros::NodeHandle& nh,float tf,VectorXf jointpos,VectorXf joint_desired);
    void getJointPos(ros::NodeHandle& nh,VectorXf& jointpos);
    void jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg);
    int pinv(Jacobian jcb, Eigen::MatrixXd &dest_mat, double tolerance = 1.e-6);
    void Jnt8to7(float Jnt8[], Vector7f& Jnt7);
    void move(ros::NodeHandle& nh,float tf,VectorXf jointpos,VectorXf joint_desired);
    void enforceJointCoupling(KDL::JntArray& q);
    double calculateError(const KDL::JntArray& q, const KDL::Frame& target);
    int invkinematics(JntArray jointpos,Frame target,JntArray& joint_desired);


};

#endif