<launch>
  <!-- 定义初始关节位置参数，可以在启动时覆盖 -->
  <arg name="initial_joint_positions" default="{
    'l_joint1': 0.0, 'l_joint2': -0.0, 'l_joint3': 0.174533, 'l_joint4': 0,
    'l_joint5': 0.0, 'l_joint6': 0.0, 'l_joint7': 0.0,
    'r_joint1': 0.0, 'r_joint2': -0.0, 'r_joint3': 0.174533, 'r_joint4': 0,
    'r_joint5': 0.0, 'r_joint6': 0.0, 'r_joint7': 0.0
  }"/>

  <param
    name="robot_description"
    textfile="$(find robot_controller)/description/urdf/S1_robot.urdf" />

  <param name="use_sim_time" value="false"/>


  <!-- 初始关节位置已通过arg参数定义 -->

  <!-- 添加joint_state_publisher节点，提供默认关节状态，但当有实际状态发布时会自动切换 -->
  <node
    name="joint_state_publisher"
    pkg="joint_state_publisher"
    type="joint_state_publisher">
    <param name="use_gui" value="false"/>
    <param name="rate" value="10"/>
    <!-- 只在没有其他节点发布关节状态时发布默认状态 -->
    <param name="publish_default_positions" value="true"/>
    <!-- 监听joint_states话题，当有其他节点发布时自动停止发布 -->
    <rosparam param="source_list">["/joint_states"]</rosparam>
    <!-- 设置默认关节位置 -->
    <rosparam param="zeros" subst_value="True">$(arg initial_joint_positions)</rosparam>
  </node>

  <node
    name="robot_state_publisher"
    pkg="robot_state_publisher"
    type="robot_state_publisher" />
  <node
    name="display_S1"
    pkg="rviz"
    type="rviz"
    args="-d $(find robot_controller)/config/robot_arm.rviz" />

  <!-- <node name="pose_rviz_visualizer" pkg="robot_controller" type="pose_rviz_visualizer.py" output="screen" /> -->
  
</launch>