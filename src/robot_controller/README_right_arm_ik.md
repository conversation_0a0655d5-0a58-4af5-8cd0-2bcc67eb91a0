# 右臂逆运动学求解器（带关节限制）

## 概述

本项目为S1机器人的右臂实现了基于Pinocchio库的高精度逆运动学求解功能，末端执行器为r_Link7。支持位置和姿态的精确求解，并包含完整的关节角度限制功能。

## 功能特性

### 1. 右臂模型构建
- 从完整的S1机器人URDF模型中提取右臂部分
- 锁定左臂、基座和手指关节，只保留右臂7个关节
- 支持的关节：r_joint1 到 r_joint7

### 2. 高精度逆运动学求解
- **统一的逆运动学接口**：同时考虑位置和旋转的高精度求解
- **位置逆运动学**：只考虑末端位置的逆运动学求解
- 使用改进的数值迭代方法（自适应阻尼最小二乘法）
- 超高精度求解：
  - 位置误差 < 0.1mm
  - 旋转误差 < 0.01度
- 自适应参数调整，提高收敛稳定性

### 3. 关节角度限制
- **默认关节限制**：基于S1机器人的实际关节限制
- **自定义关节限制**：支持用户自定义关节角度范围
- **实时限制检查**：求解过程中自动应用关节限制
- **限制验证**：提供关节角度范围检查功能

### 4. 正运动学计算
- 根据关节角度计算末端执行器位姿
- 支持SE3变换矩阵输出

## 文件结构

```
src/robot_controller/
├── include/robot_controller/
│   └── right_arm_ik_solver.h          # 头文件
├── src/
│   ├── right_arm_ik_solver.cpp        # 实现文件
│   └── module_test/
│       ├── test_pinocchio_fk2.cpp     # 原始测试程序
│       ├── test_right_arm_ik_solver.cpp # 求解器类测试程序
│       ├── test_improved_ik.cpp       # 改进逆运动学测试
│       └── test_joint_limits.cpp      # 关节限制功能测试
└── README_right_arm_ik.md             # 本文档
```

## 使用方法

### 1. 基本使用

```cpp
#include "robot_controller/right_arm_ik_solver.h"

// 创建求解器实例
std::string urdf_path = "/path/to/S1_robot.urdf";
RightArmIKSolver ik_solver(urdf_path, "r_Link7");

// 检查初始化状态
if (!ik_solver.isInitialized()) {
    std::cerr << "求解器初始化失败" << std::endl;
    return -1;
}
```

### 2. 高精度逆运动学求解（推荐）

```cpp
// 设置目标位姿
pinocchio::SE3 target_pose;
target_pose.translation() << 0.5, -0.2, 0.8;
target_pose.rotation() = Eigen::Matrix3d::Identity();

// 设置初始关节角度
Eigen::VectorXd q_init = Eigen::VectorXd::Zero(7);

// 高精度求解
Eigen::VectorXd q_solution;
bool success = ik_solver.solveIK(target_pose, q_init, q_solution,
                                1000,   // 最大迭代次数
                                1e-4,   // 位置容差
                                1e-4,   // 旋转容差
                                1.0,    // 位置权重
                                0.5);   // 旋转权重

if (success) {
    std::cout << "求解成功！关节角度: " << q_solution.transpose() << std::endl;
}
```

### 3. 位置逆运动学求解

```cpp
// 设置目标位置
Eigen::Vector3d target_position(0.5, -0.2, 0.8);

// 求解
Eigen::VectorXd q_solution;
bool success = ik_solver.solvePositionIK(target_position, q_init, q_solution);

if (success) {
    std::cout << "求解成功！关节角度: " << q_solution.transpose() << std::endl;
}
```

### 4. 关节限制设置

```cpp
// 设置自定义关节限制（单位：弧度）
Eigen::VectorXd q_min(7), q_max(7);
q_min << -2.0, -1.0, -2.0, -1.5, -2.0, -1.0, -2.0;
q_max <<  2.0,  1.0,  2.0,  1.5,  2.0,  1.0,  2.0;

ik_solver.setJointLimits(q_min, q_max);

// 检查关节角度是否在限制范围内
bool within_limits = ik_solver.isWithinJointLimits(q_solution);

// 获取默认关节限制
Eigen::VectorXd default_q_min, default_q_max;
ik_solver.getDefaultJointLimits(default_q_min, default_q_max);
```

### 5. 正运动学计算

```cpp
// 给定关节角度
Eigen::VectorXd q(7);
q << 0.308, 0.688, 0.295, 0.457, 0.161, 0.326, 0.598;

// 计算末端位姿
pinocchio::SE3 end_pose = ik_solver.computeForwardKinematics(q);

std::cout << "末端位置: " << end_pose.translation().transpose() << std::endl;
std::cout << "末端旋转:\n" << end_pose.rotation() << std::endl;
```

## 编译和测试

### 1. 编译

```bash
cd /path/to/workspace
catkin_make --only-pkg-with-deps robot_controller
```

### 2. 运行测试

```bash
# 运行高精度逆运动学测试
source devel/setup.bash
rosrun robot_controller test_improved_ik

# 运行关节限制功能测试
rosrun robot_controller test_joint_limits

# 运行基础求解器测试
rosrun robot_controller test_right_arm_ik_solver

# 运行原始测试程序
rosrun robot_controller test_pinocchio_fk2
```

## 测试结果

### 高精度逆运动学测试结果：
- **测试1（已知解）**：成功，位置误差 < 0.05mm，旋转误差 < 0.005度
- **测试2（多种姿态）**：5/5成功，所有测试都满足高精度要求
- **批量测试**：100%成功率

### 关节限制测试结果：
- **默认限制**：±180°（大部分关节），±90°（部分关节）
- **自定义限制**：成功应用更严格的关节限制
- **限制检查**：求解过程中自动应用关节限制
- **极限位置**：超出工作空间的位置正确识别并报告失败

### 性能指标：
- **收敛速度**：通常在150-200次迭代内收敛
- **位置精度**：< 0.1mm
- **旋转精度**：< 0.01度
- **成功率**：在工作空间内100%

## API参考

### RightArmIKSolver类

#### 构造函数
```cpp
RightArmIKSolver(const std::string& urdf_path, 
                 const std::string& end_effector_name = "r_Link7");
```

#### 主要方法

- `bool solveIK(...)` - 高精度逆运动学求解（位置+姿态）
- `bool solvePositionIK(...)` - 位置逆运动学求解
- `pinocchio::SE3 computeForwardKinematics(...)` - 正运动学计算
- `void setJointLimits(...)` - 设置关节角度限制
- `bool isWithinJointLimits(...)` - 检查关节角度是否在限制范围内
- `void getDefaultJointLimits(...)` - 获取默认关节限制
- `bool isInitialized()` - 检查初始化状态
- `int getJointCount()` - 获取关节数量
- `void setParameters(...)` - 设置求解器参数

## 注意事项

1. **推荐使用高精度逆运动学**：`solveIK`方法现在能同时精确求解位置和姿态
2. **关节限制**：默认启用关节限制，可根据需要自定义
3. **初始值选择**：合理的初始关节角度有助于更快收敛
4. **工作空间限制**：确保目标位置在机械臂的可达工作空间内
5. **参数调整**：可通过`setParameters`方法调整求解器参数以适应不同需求

## 依赖项

- ROS Noetic
- Pinocchio 3.6.0+
- Eigen3
- URDF模型文件

## 作者

基于Pinocchio库实现的S1机器人右臂逆运动学求解器。
