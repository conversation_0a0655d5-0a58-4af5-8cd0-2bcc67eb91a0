#include "plan.h"

int main(int argc,char** argv) 
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "kinetics_solver");
    ros::NodeHandle nh;
    int nj = 8;
    const char* arm = "left";
    const char* urdf_path = "/home/<USER>/catkin_ws/src/robot_arm/urdf/dualarm.urdf";
    robot_control robot(urdf_path,arm);

    KDL::Frame target;
    target.p = KDL::Vector( 0.51754, 0.0664322, -0.609469); 
    target.M = Rotation::RotX(PI/2) * Rotation::RotY(PI);
    KDL::JntArray q_init(nj);
    Vector8f vec_tar;
    vec_tar << -23.9677,   10.7321,   0,   27.4297,   27.4297 ,  45.8967,   23.9574 ,  0.0590966 ;
    for(size_t i = 0;i < nj; i++)
    {
        q_init(i) = vec_tar[i];
    }
    Matrix4 T = robot.forwardKinematics(vec_tar);
    cout << T << endl;
    KDL::JntArray q_sol(nj);
    int result = robot.invkinematics(q_init, target, q_sol);
    if (result != 0) {
        std::cerr << "IK solver failed with error code: " << result << std::endl;
        return -1;
    }

    float JntCalcted8[8];
    float tf = 3;
    Vector7f q_goal;
    Vector14f q_current = Vector14f::Zero();
    Vector14f q_end = Vector14f::Zero();
    // 右臂中间值
    q_end(8) = -1.4;
    q_end(10) = 1.1;
    //左臂中间数值
    // q_end(1) = -1.4;
    // q_end(3) = 1.1;
    robot.pub2Rviz(nh,tf,q_current,q_end);//仿真中共14个关节

    q_current = q_end;
    for(size_t i = 0; i < 8;i++)
    {
        JntCalcted8[i] = q_sol(i);
        cout << q_sol(i) << endl;
    }
    robot.Jnt8to7(JntCalcted8, q_goal);
    for(size_t i = 0; i < 7; i++)
    {
        q_end(i+7) = q_goal(i);
    }
    robot.pub2Rviz(nh,tf,q_current,q_end);

    // target.p[2] += 0.10;
    // result = robot.invkinematics(q_init, target, q_sol);
    // if (result != 0) {
    //     std::cerr << "IK solver failed with error code: " << result << std::endl;
    //     return -1;
    // }
    // q_current = q_end;
    // for(size_t i = 0; i < 8;i++)
    // {
    //     JntCalcted8[i] = q_sol(i);
    // }

    // robot.Jnt8to7(JntCalcted8, q_goal);
    // for(size_t i = 0; i < 7; i++)
    // {
    //     q_end(i+7) = q_goal(i);
    // }
    // robot.pub2Rviz(nh,tf,q_current,q_end);
    return 0;
}