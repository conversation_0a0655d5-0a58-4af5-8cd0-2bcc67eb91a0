#include <iostream>
#include <vector>
#include <kdl/kdl.hpp> 
#include <kdl/chain.hpp> 
#include <kdl/tree.hpp> 
#include <kdl/segment.hpp> 
#include <kdl_parser/kdl_parser.hpp> 
#include <kdl/chainfksolverpos_recursive.hpp> 
#include <kdl/chainjnttojacsolver.hpp>
#include <kdl/frames_io.hpp> 
#include <kdl/utilities/error.h>
#include <kdl/chainiksolverpos_nr.hpp>
#include <kdl/chainiksolverpos_lma.hpp>
#include <kdl/chainiksolver.hpp>
#include <kdl/chainfksolver.hpp>
#include <kdl/chainiksolverpos_nr_jl.hpp>
#include <stdio.h> 
#include <iostream> 
#include <sys/times.h>
#include <unistd.h>
#include <stdlib.h>
#include <kdl/chainiksolvervel_pinv.hpp>

using namespace std;
using namespace KDL;
// 7自由度机械臂逆运动学求解器（带关节范围限制）
class KDLArmIKSolver {
public:
    KDLArmIKSolver() {
        // 1. 构建7自由度机械臂模型（示例DH参数，需替换为实际参数）
        // 关节0-5为旋转关节，关节6为旋转关节（末端）
/*         chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotZ),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.1))  // 基座到关节1
        ));
        chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotY),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.4))  // 关节1到关节2
        ));
        chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotY),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.35)) // 关节2到关节3
        ));
        chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotZ),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.1))  // 关节3到关节4
        ));
        chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotY),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.3))  // 关节4到关节5
        ));
        chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotZ),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.1))  // 关节5到关节6
        ));
        chain.addSegment(KDL::Segment(
            KDL::Joint(KDL::Joint::RotY),
            KDL::Frame(KDL::Vector(0.0, 0.0, 0.15)) // 关节6到末端执行器
        )); */
        Tree my_tree; 
        kdl_parser::treeFromFile("/home/<USER>/rope_driven_ws/src/robot_arm/urdf/dualarm.urdf",my_tree); 
        bool exit_value;
        int error_success = 0;
        exit_value = my_tree.getChain("base_0","r_Link8",chain); 
        if(!exit_value){ 
            cout << "Error:could not find chain arm_right"<<endl; 
        } 
        // 2. 初始化正运动学求解器
        fk_solver.reset(new KDL::ChainFkSolverPos_recursive(chain));
        ik_vel_solver.reset(new KDL::ChainIkSolverVel_pinv(chain));
        // 3. 设置关节范围限制（所有关节初始范围，后续重点限制6和7关节）
        q_min.resize(7);
        q_max.resize(7);
        
        // 关节0-5设置默认范围（示例值，需根据实际机械臂调整）
        for (int i = 0; i < 7; ++i) {
            q_min(i) = -M_PI;    // -π
            q_max(i) = M_PI;     // π
        }

        // 重点：限制第2关节（索引6）范围 
        q_max(1) = 18 * M_PI / 180;  // 8度

        // 4. 初始化带关节限制的逆运动学求解器
        ik_solver.reset(new KDL::ChainIkSolverPos_NR_JL(
            chain,          // 机械臂模型
            q_min,          // 关节最小值
            q_max,          // 关节最大值
            *fk_solver,     // 正运动学求解器
            *ik_vel_solver, // 速度逆运动学求解器
            100,            // 最大迭代次数
            1e-6            // 精度
        ));
    }

    // 求解逆运动学
    bool solveIK(const KDL::Frame& target_pose, KDL::JntArray& q_out, const KDL::JntArray& q_init = KDL::JntArray(7)) {
        if (q_init.rows() != 7) {
            std::cerr << "初始关节角度必须为7维" << std::endl;
            return false;
        }

        // 复制初始关节角度作为迭代起点
        KDL::JntArray q_sol = q_init;

        // 调用KDL求解器
        int result = ik_solver->CartToJnt(q_sol, target_pose, q_out);

        // 检查求解结果
        if (result < 0) {
            std::cerr << "逆运动学求解失败，错误代码: " << result << std::endl;
            return false;
        }

        // 额外验证：确保第2关节在限制范围内（双重保险）
        if (q_out(1) > q_max(1)) {
            std::cerr << "第7关节超出限制范围: " << q_out(1) << std::endl;
            return false;
        }

        return true;
    }

private:
    KDL::Chain chain;  // 机械臂模型
    std::unique_ptr<KDL::ChainFkSolverPos_recursive> fk_solver;  // 正运动学求解器
    std::unique_ptr<KDL::ChainIkSolverPos_NR_JL> ik_solver;      // 带关节限制的逆运动学求解器
    std::unique_ptr<KDL::ChainIkSolverVel_pinv> ik_vel_solver; 
    KDL::JntArray q_min;  // 关节最小值
    KDL::JntArray q_max;  // 关节最大值
};

int main() {
    // 创建求解器实例
    KDLArmIKSolver ik_solver;

    // 目标位姿：末端执行器的位置和姿态
    KDL::Frame target_pose;
    target_pose.p = KDL::Vector(0.25,   0.05,   -0.3);  // 位置 (x, y, z)
    target_pose.M = KDL::Rotation::RotZ(KDL::PI/2);          // 单位旋转矩阵

    // 初始关节角度（迭代起点）
    KDL::JntArray q_init(7); // 初始化为零位

    // 求解逆运动学
    KDL::JntArray q_sol(7);
    bool success = ik_solver.solveIK(target_pose, q_sol, q_init);

    if (success) {
        std::cout << "逆运动学求解成功，关节角度为：" << std::endl;
        for (int i = 0; i < 7; ++i) {
            std::cout << "关节 " << i+1 << ": " << q_sol(i) << " 弧度" << std::endl;
        }
        
        // // 特别输出第6和第7关节，验证是否在限制范围内
        // std::cout << "\n第6关节范围验证: " << q_sol(5) << " (限制: [-" << M_PI/2 << ", " << M_PI/2 << "])" << std::endl;
        // std::cout << "第7关节范围验证: " << q_sol(6) << " (限制: [-" << M_PI/4 << ", " << M_PI/4 << "])" << std::endl;
    }

    return 0;
}
