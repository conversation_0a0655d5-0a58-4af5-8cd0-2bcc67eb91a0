#include "plan.h"

int main(int argc,char** argv) 
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "kinetics_solver");
    ros::NodeHandle nh;
    int nj = 8;
    const char* arm = "right";
    const char* urdf_path = "/home/<USER>/catkin_ws/src/robot_arm/urdf/dualarm.urdf";
    robot_control robot(urdf_path,arm);
    robot_control left_arm(urdf_path,"left");

    KDL::Frame target;
    target.p = KDL::Vector( 0.633862, -0.10619, -0.550599); 
    target.M = KDL::Rotation::RotX(KDL::PI/2) * KDL::Rotation::RotY(KDL::PI/2);
    KDL::JntArray q_init(nj);
    // q_init(2) = 0.6;
    // q_init(3) = 0.85;
    KDL::JntArray q_sol(nj);
    int result = robot.invkinematics(q_init, target, q_sol);
    if (result != 0) {
        std::cerr << "IK solver failed with error code: " << result << std::endl;
        return -1;
    }
    float JntCalcted8[8];
    float tf = 3;
    Vector7f q_goal;
    Vector14f q_current = Vector14f::Zero();
    Vector14f q_end = Vector14f::Zero();
    // 右臂中间值
    q_end(8) = -1.4;
    q_end(10) = 1.1;
    //左臂中间值
    q_end(1) = -1.4;
    q_end(3) = 1.1;
    // robot.pub2Rviz(nh,tf,q_current,q_end);//仿真中共14个关节

    tf = 5;
    q_current = q_end;
    cout << "q_sol:" << endl;
    for(size_t i = 0; i < 8;i++)
    {
        JntCalcted8[i] = q_sol(i);
        cout << q_sol(i) << " ";
    }
    cout << endl;
    robot.Jnt8to7(JntCalcted8, q_goal);
    for(size_t i = 0; i < 7; i++)
    {
        q_end(i+7) = q_goal(i);
    }
    // robot.pub2Rviz(nh,tf,q_current,q_end);
    cout << "-----------------移动到旋转瓶盖的位置----------------------" << endl;
    q_current = q_end;
//右臂求逆解
    Frame tmp_r;
    tmp_r.p = Vector(0.2,-0.1,-0.40);
    // tmp_r.M = Rotation::RotX(PI/2) * Rotation::RotY(PI);
    tmp_r.M = target.M;
    // tmp_r.M = target.M * Rotation::RotZ(-PI/4);
    q_init(2) = 2;
    // q_init(3) = 0.85;
    result = robot.invkinematics(q_init, tmp_r, q_sol);
    if (result != 0) 
    {
        std::cerr << "right IK solver failed with error code: " << result << std::endl;
        return -1;
    }
    for(size_t i = 0; i < 8;i++)
    {
        JntCalcted8[i] = q_sol(i);
    }

    robot.Jnt8to7(JntCalcted8, q_goal);
    for(size_t i = 0; i < 7; i++)
    {
        q_end(i+7) = q_goal(i);
    }
    // q_end(13) += PI/4;
//左臂求逆解
    // Frame tmp_l;
    // tmp_l.p = tmp_r.p + Vector(0.0,0.20,0.1) ;
    // tmp_l.M = Rotation::RotX(PI/2);
    // result = left_arm.invkinematics(q_init, tmp_l, q_sol);
    // if (result != 0) {
    //     std::cerr << "left IK solver failed with error code: " << result << std::endl;
    //     return -1;
    // }
    // for(size_t i = 0; i < 8;i++)
    // {
    //     JntCalcted8[i] = q_sol(i);
    // }

    // robot.Jnt8to7(JntCalcted8, q_goal);
    // for(size_t i = 0; i < 7; i++)
    // {
    //     q_end(i) = q_goal(i);
    // }

    // robot.pub2Rviz(nh,tf,q_current,q_end);

    return 0;
}