#include "plan.h"
#include <std_msgs/UInt8MultiArray.h>
#include <std_msgs/Bool.h>
#include <robotiq_2f_gripper_msgs/CommandRobotiqGripperActionGoal.h>


bool detect_flag = false;
vector<double> container;
bool return_flag = false;

void camera_array_callback(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "orange: " << value << endl;
    }
}

int main(int argc, char **argv)
{
    ros::init(argc, argv, "pour_water");
    ros::NodeHandle nh;
    ros::Publisher right_pub_ = nh.advertise<robotiq_2f_gripper_msgs::CommandRobotiqGripperActionGoal>(
            "/right_gripper/command_robotiq_action/goal", 1);
    ros::Publisher left_pub_ = nh.advertise<robotiq_2f_gripper_msgs::CommandRobotiqGripperActionGoal>(
            "/left_gripper/command_robotiq_action/goal", 1);
    ros::Publisher jnt_cur_pub = nh.advertise<std_msgs::Float64MultiArray>("/return_init", 1);
    ros::Publisher jnt_pub = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    std_msgs::Float64MultiArray jnt_msg;
    jnt_msg.data.resize(16);
    ros::Subscriber sub = nh.subscribe("gripper_det_box", 1, camera_array_callback);
    ros::Rate loop_rate(50);
    std_msgs::Bool pose_data;
    const char* arm = "left";
    robot_control robot(arm);
    float q_cur[8] = {0};
    float JntCalcted8[8] = {0};
    float Target[4][4] = {0};
    float tf = 15;
    int sucess_exit = 0;
    Vector8f q_current = Vector8f::Zero();
    vector<double> target_pos(3);
    Vector8f q_tmp_l_1st = Vector8f::Zero();
    Vector8f q_tmp_l_2nd = Vector8f::Zero();
    Vector8f q_tmp_r = Vector8f::Zero();
    Vector8f q_tar = Vector8f::Zero();
    double position_r = 0.06;
    double position_l = 0.03;
    int count = 0;
    // 动态速度 (开得越大越快)
    double speed = 0.08;  // 0.02 ~ 0.1 m/s
    // 动态力度 (闭得越紧越大)
    double force = 100;  // 20 ~ 200 N
    // 构建ActionGoal消息
    robotiq_2f_gripper_msgs::CommandRobotiqGripperActionGoal cmd;
    cmd.goal.position = position_r;
    cmd.goal.speed = speed;
    cmd.goal.force = force;
    // Matrix4 T = robot.forwardKinematics(q_current);
    // cout << "当前变换矩阵：" << T << endl;
    Rotation R = Rotation::RotX(PI/2) * Rotation::RotY(PI/2);
    for(size_t i = 0; i < 3; i++)
    {
        for(size_t j = 0; j < 3; j++)
        {
            Target[i][j] = R(i,j);
        }
    }
    //获取目标位置
    while(!detect_flag && ros::ok())
    {
        ros::spinOnce();
        usleep(1000);
        ROS_INFO("Don't detect target!");
    }
    cout << container.data() << endl;
    while (container.size() >= 3) 
    {
        for (int i = 0; i < 3; ++i) 
        { 
            target_pos[i]=container[i];
        }
        container.erase(container.begin(), container.begin() + 3);
    }
    cout << "target_pos:";
    for(size_t i = 0;i < 3;i++)
    {
        cout << target_pos[i] << " ";
    }
    cout << endl;
    Target[0][3] = target_pos[0] - 0.15;
    Target[1][3] = target_pos[1] - 0.165;//y轴相对于基座有偏移
    Target[2][3] = target_pos[2] + 0.06;
    cout << "target1:" << Target[0][3] << " " << Target[1][3] << " " << Target[2][3]<< endl;

    // Target[0][3] = 0.5;
    // Target[1][3] = -0.13;//y轴相对于基座有偏移
    // Target[2][3] = -0.3;
    sucess_exit = robot.nfInvKinePosLev(Target, q_cur, JntCalcted8);
    if(sucess_exit == 0)
    {
        ROS_ERROR("IK failed");
        return 0;
    }
    for(size_t i = 0; i < 8; i++)
    {
        cout << JntCalcted8[i] << endl;
        // q_tar(i) = JntCalcted8[i];
    }
    Vector16f q_init = Vector16f::Zero();
    Vector16f q_end = Vector16f::Zero();
    // 右臂中间值
    // q_end(9) = 1.2;
    // q_end(11) = 0.9;

    q_end(1) = 1.2;
    q_end(3) = 0.9;
    // jnt_msg.data[9] = 1.2;
    // jnt_msg.data[11] = 1.0;
    cout << "接近目标物体！" << endl;
    robot.move(nh,tf,q_init,q_end);
    // jnt_pub.publish(jnt_msg);

    sleep(2);
    q_init = q_end;
    for(size_t i = 0; i < 8; i++)
    {
        q_end(i) = JntCalcted8[i];
        // q_end(i+8) = JntCalcted8[i];//右臂
        q_cur[i] = JntCalcted8[i];

    }
    robot.move(nh,tf,q_init,q_end);

    Target[0][3] = target_pos[0];
    Target[1][3] = target_pos[1] - 0.165 ;//y轴相对于基座有偏移
    Target[2][3] = target_pos[2] + 0.04;
    cout << "target2:" << Target[0][3] << " " << Target[1][3] << " " << Target[2][3]<< endl;
    sucess_exit = robot.nfInvKinePosLev(Target, q_cur, JntCalcted8);
    if(sucess_exit == 0)
    {
        ROS_ERROR("IK failed");
        return 0;
    }
    cout << "第二段逆解：";
    for(size_t i = 0; i < 8; i++)
    {
        cout << JntCalcted8[i] << " ";
        q_tar(i) = JntCalcted8[i];
        jnt_msg.data[i+8] = JntCalcted8[i];
    }
    cout << endl;
    sleep(4);
    q_init = q_end;
    tf = 10;
    for(size_t i = 0; i < 8; i++)
    {
        q_end(i) = JntCalcted8[i];
        // q_end(i+8) = JntCalcted8[i];//右臂

    }
    cout << "靠近目标物体！" << endl;
    robot.move(nh,tf,q_init,q_end);

    sleep(4);
    /* 机械臂回零 */
    // pose_data.data = true;
    // pub.publish(pose_data);

    cout << "右手夹爪夹持！" << endl;
    while (count < 5)
    {
        count++;
        right_pub_.publish(cmd);

    }
    // sleep(4);
    // jnt_msg.data[9] = -jnt_msg.data[9];
    // jnt_msg.data[14] = -jnt_msg.data[14];
    // jnt_msg.data[15] = -jnt_msg.data[15];

    // jnt_cur_pub.publish(jnt_msg);
    cout << "Sport is over!" << endl;
    return 0;

    /* 确保抓住 */
    /*     while(ros::ok() && !grasp_finshed_r)
    {
        finger_pub.publish(finger_data);
        ros::spinOnce();
        loop_rate.sleep();
        ROS_INFO("grasp_finshed_r = %d",grasp_finshed_r);
    } */
    /* 移动到水杯上方,递出去 */
    cout << "右手将杯子拿到胸前！" << endl;
    q_init = q_end;
    q_end(8) = -1.6;
    q_end(9) = 1.15;
    q_end(10) = -0.3;
    q_end(11) = 1.15;
    q_end(12) = 0.3;
    q_end(13) = 0.3;
    q_end(14) = 0.3;
    q_end(15) = 0.3;
    robot.move(nh,tf,q_init,q_end);
/* 右手临时数据记录 */
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_tmp_r(idx) = q_end(idx+8);
    }

    cout << "左手接近瓶盖！" << endl;
    q_init = q_end;
    //左臂中间数值
    q_end(1) = -1.4;
    q_end(3) = 1.1;
    robot.move(nh,tf,q_init,q_end);

    tf = 8;
    q_init = q_end;
    q_end(0) = -0.5;
    q_end(1) = -1.4;
    q_end(2) = 0.5;
    q_end(3) = 1.1;
    q_end(4) = 0.5;
    q_end(5) = 0.5;
    q_end(6) = 0.5;
    q_end(7) = 0.5;
    robot.move(nh,tf,q_init,q_end);
/* 左手临时数记录，杯子上方10cm */
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_tmp_l_1st(idx) = q_end(idx);
    }

    cout << "左手下降接近瓶盖！" << endl;
    q_init = q_end;
    tf = 4;
    for(size_t i = 0; i < 8; i++)
    {
        q_current(i) = q_end(i);
    }
    Matrix4 T = robot.forwardKinematics(q_current);
    cout << "当前变换矩阵：" << T << endl;
    T(2,3) = T(2,3) - 0.05;
    for(size_t i = 0; i < 4; i++)
    {
        for(size_t j = 0; j < 4; j++)
        {
            Target[i][j] = T(i,j);
        }
    }
    sucess_exit = robot.nfInvKinePosLev(Target, q_cur, JntCalcted8);
    if(sucess_exit == 0)
    {
        ROS_ERROR("IK failed");
        return 0;
    }
    cout << "下降时关节角度：" ;
    for(size_t i = 0; i < 8; i++)
    {
        cout << JntCalcted8[i] << " ";
        q_end(i) = JntCalcted8[i];
        q_tmp_l_2nd(i) = JntCalcted8[i];
    }
    cout << endl;
    robot.move(nh,tf,q_init,q_end);

    cout << "左手夹爪加持！" << endl;
    cmd.goal.position = position_l;
    left_pub_.publish(cmd);
    sleep(1);


    cout << "左手旋盖！" << endl;
    q_init = q_end;
    tf = 4;
    q_end(7) -= PI/2;
    robot.move(nh,tf,q_init,q_end);

    q_init = q_end;
    tf = 3;
    cout << "左手拿起瓶盖！" << endl;
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_end(idx) = q_tmp_l_2nd(idx);
    }
    robot.move(nh,tf,q_init,q_end);
    /* 左手第二段移动到合适位置 */
    tf = 5;
    q_init = q_end;
    q_end(0) = -0.5;
    q_end(1) = -1.4;
    q_end(2) = 0.5;
    q_end(3) = 1.1;
    q_end(4) = 0.5;
    q_end(5) = 0.5;
    q_end(6) = 0.5;
    q_end(7) = 0.5;
    robot.move(nh,tf,q_init,q_end);

    cout << "右手移动到杯子上方!" << endl;
    q_init = q_end;
    tf = 5;
    q_end(8) = -0.5;
    q_end(9) = -1.4;
    q_end(10) = 0.5;
    q_end(11) = 1.1;
    q_end(12) = 0.5;
    q_end(13) = 0.5;
    q_end(14) = 0.5;
    q_end(15) = 0.5;
    robot.move(nh,tf,q_init,q_end);

    cout << "右手去倒水!" << endl;
    q_init = q_end;
    tf = 3;
    q_end(15) += PI/3;
    robot.move(nh,tf,q_init,q_end);

    cout << "右手回到胸前！" << endl;
    q_init = q_end;
    tf = 5;
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_end(idx+8) = q_tmp_r(idx);
    }

    robot.move(nh,tf,q_init,q_end);

    cout << "左手回到胸前！" << endl;
/* 左手回到杯子上方 */
    q_init = q_end;
    tf = 8;
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_end(idx) = q_tmp_l_1st(idx);
    }
    robot.move(nh,tf,q_init,q_end);

    cout << "左手向下运动！" << endl;
    q_init = q_end;
    tf = 5;
/* 再向下运动 */
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_end(idx) = q_tmp_l_2nd(idx);
    }
    robot.move(nh,tf,q_init,q_end);

    q_init = q_end;
    tf = 4;
    cout << "左手回旋！" << endl;
    q_end(7) += PI/2;
    robot.move(nh,tf,q_init,q_end);

/* 最后一段 */
    q_init = q_end;
    for(size_t idx = 0; idx < 8; idx++)
    {
        q_end(idx) = q_tmp_l_1st(idx);
        q_end(idx+8) = q_tar(idx);
    }
    tf = 5;
    robot.move(nh,tf,q_init,q_end);

    cout << "右手夹爪松开！" << endl;
    position_r = 0.1;
    cmd.goal.position = position_r;
    right_pub_.publish(cmd);

    // cout << "机械臂归零！" << endl;
    // pose_data.data = true;
    // pub.publish(pose_data);

    return 0;
}