#include <plan.h>

/* 灵巧手关节角度->电机角度 */
vector<int> angleToMotor(const std::vector<double>& radian_angles) 
{
    if (radian_angles.empty() || radian_angles.size() < 15) {
        std::cout << "angleToMotor 输入不合法，长度不足15！" << std::endl;
        return {};
    }

    std::vector<int> motors(15, 0);
    std::vector<double> angles(15);

    // 将弧度转换为角度
    for (int i = 0; i < 15; ++i) {
        angles[i] = radian_angles[i] * 180.0 / M_PI;
    }

    // 指跟俯仰关节
    if (angles[0] >= 0) {
        motors[13] = -static_cast<int>(angles[0] * 10);
        motors[12] = static_cast<int>(angles[0] * 10);
    } else {
        motors[13] = static_cast<int>(angles[0] * 10);
        motors[12] = -static_cast<int>(angles[0] * 10);
    }

    if (angles[3] >= 0) {
        motors[9] = static_cast<int>(angles[3] * 10);
        motors[10] = -static_cast<int>(angles[3] * 10);
    } else {
        motors[9] = -static_cast<int>(angles[3] * 10);
        motors[10] = static_cast<int>(angles[3] * 10);
    }

    if (angles[6] >= 0) {
        motors[6] = static_cast<int>(angles[6] * 10);
        motors[7] = -static_cast<int>(angles[6] * 10);
    } else {
        motors[6] = -static_cast<int>(angles[6] * 10);
        motors[7] = static_cast<int>(angles[6] * 10);
    }

    if (angles[9] >= 0) {
        motors[3] = static_cast<int>(angles[9] * 10);
        motors[4] = -static_cast<int>(angles[9] * 10);
    } else {
        motors[3] = -static_cast<int>(angles[9] * 10);
        motors[4] = static_cast<int>(angles[9] * 10);
    }

    if (angles[12] >= 0) {
        motors[0] = -static_cast<int>(angles[12] * 10);
        motors[1] = static_cast<int>(angles[12] * 10);
    } else {
        motors[0] = static_cast<int>(angles[12] * 10);
        motors[1] = -static_cast<int>(angles[12] * 10);
    }

    // 指中俯仰关节
    motors[2] = static_cast<int>(angles[14] * 10);
    motors[5] = static_cast<int>(angles[11] * 10);
    motors[8] = static_cast<int>(angles[8] * 10);
    motors[11] = static_cast<int>(angles[5] * 10);
    motors[14] = static_cast<int>(angles[5] * 10);  // 原Python代码中此处可能是笔误，保持与原逻辑一致

    // 侧摆关节
    if (angles[1] >= 0) {
        motors[13] += static_cast<int>(angles[1] * 10);
        motors[12] += static_cast<int>(angles[1] * 10);
    } else {
        motors[13] -= static_cast<int>(angles[1] * 10);
        motors[12] -= static_cast<int>(angles[1] * 10);
    }

    if (angles[4] >= 0) {
        motors[9] += static_cast<int>(angles[4] * 10);
        motors[10] += static_cast<int>(angles[4] * 10);
    } else {
        motors[9] -= static_cast<int>(angles[4] * 10);
        motors[10] -= static_cast<int>(angles[4] * 10);
    }

    if (angles[7] >= 0) {
        motors[6] += static_cast<int>(angles[7] * 10);
        motors[7] += static_cast<int>(angles[7] * 10);
    } else {
        motors[6] -= static_cast<int>(angles[7] * 10);
        motors[7] -= static_cast<int>(angles[7] * 10);
    }

    if (angles[10] >= 0) {
        motors[3] += static_cast<int>(angles[10] * 10);
        motors[4] += static_cast<int>(angles[10] * 10);
    } else {
        motors[3] -= static_cast<int>(angles[10] * 10);
        motors[4] -= static_cast<int>(angles[10] * 10);
    }

    if (angles[14] >= 0) {  // 原Python代码中此处可能是笔误，保持与原逻辑一致
        motors[0] += static_cast<int>(angles[14] * 10);
        motors[1] += static_cast<int>(angles[14] * 10);
    } else {
        motors[0] -= static_cast<int>(angles[14] * 10);
        motors[1] -= static_cast<int>(angles[14] * 10);
    }

    return motors;
}

/*	多项式插值待定参数计算
    功能：考虑起点、终点速度、加速度为0的简化情况，计算多项式插值待定参数
    输入：float q0，初始状态
          float qf，终止状态
          float tf，运动时间
    输出：float a0345[4]，多项式参数，a0, a3, a4, a5
*/
void Interp3rdPoly_Param_P2P_Zrros_fcn(float q0, float qf, float tf, float a0345[4])
{
    float tf_2, tf_3, tf_4, tf_5;

    tf_2 = tf * tf;
    tf_3 = tf_2 * tf;
    tf_4 = tf_3 * tf;
    tf_5 = tf_4 * tf;

    a0345[0] = q0;
    a0345[1] = 10 * (qf - q0) / tf_3;
    a0345[2] = -15 * (qf - q0) / tf_4;
    a0345[3] = 6 * (qf - q0) / tf_5;
    return;
}

/*多项式插值

    功能：考虑起点、终点速度、加速度为0的简化情况，给定5次多项式参数和当前时间，规划当前的关节角，角速度，角加速度
    输入：float tt，时间变量，循环变量
          float a0345[4]，多项式参数，a0, a3, a4, a5
    输出：float qq_data[3] 关节参数，包括关节角，角速度，角加速度
*/
void Interp3rdPoly_Data_t_P2P_Zrros_fcn(float tt, float a0345[4], float qq_data[3])
{
    float a0, a3, a4, a5, qt, qvt, qat;
    float tt_2, tt_3, tt_4, tt_5;

    tt_2 = tt * tt;
    tt_3 = tt_2 * tt;
    tt_4 = tt_3 * tt;
    tt_5 = tt_4 * tt;

    a0 = a0345[0];
    a3 = a0345[1];
    a4 = a0345[2];
    a5 = a0345[3];

    qt = a0 + a3 * tt_3 + a4 * tt_4 + a5 * tt_5;
    qvt = 3 * a3 * tt_2 + 4 * a4 * tt_3 + 5 * a5 * tt_4;
    qat = 6 * a3 * tt + 12 * a4 * tt_2 + 20 * a5 * tt_3;

    qq_data[0] = qt;
    qq_data[1] = qvt;
    qq_data[2] = qat;
    return;
}
/************************************************************************/
/*			关节空间轨迹规划（多关节）
   功能：机器人根据期望角度指令规划关节路径算法运动到期望位置，到达目标后速度为零；
   输入：float Tx时间变量，循环变量
        int   JNT_NUM，关节数 5
        float Angle0[]初始角
        float Angled[]期望角
        float RestrainPara_time ,初始运动时间tf
        float RestrainPara_MaxVel ,最大角速度限制
        float RestrainPara_ACC，最大角加速度限制
   输出：float JointCmd_Time运动时间（根据初始值、最大速度、最大加速度限制得到的tf）
         float fDesiredJntAngleDeg[]：输出变量，期望关节角度
         float fDesiredJntRateDeg []：输出变量，期望期望关节角速度
         float fDesiredJntAccDeg []：输出变量，期望期望关节加速度
*/
/************************************************************************/
void MultipleJointP2PPlan(float Tx,int JNT_NUM, float tf,float Angle0[], float Angled[], float fDesiredJntAngleDeg[], float fDesiredJntRateDeg[], float fDesiredJntAccDeg[])
{
    // float tf1, tf2, tf_Jnt_i, qv_limit, qa_limit, q0, qf, abs_dq;
    float a0345[4], qq_data[3];
    int i;

/*     qv_limit = RestrainPara_MaxVel;
    qa_limit = RestrainPara_ACC; */

    /************************************************************************/
    /* 确定所有关节满足约束条件的tf                                       */
    /************************************************************************/
/*     for (i = 0; i < JNT_NUM; i++)
    {
        q0 = Angle0[i];
        qf = Angled[i];

        abs_dq = (float)(abs(qf - q0));

        tf1 = abs_dq / qv_limit;
        tf2 = (float)(sqrt(2 * abs_dq / qa_limit));

        if (tf_Jnt_i < tf1)
        {
            tf_Jnt_i = tf1;
        }
        if (tf_Jnt_i < tf2)  
        {
            tf_Jnt_i = tf2;   
        }

        if (tf < tf_Jnt_i)
        {
            tf = tf_Jnt_i;
        }
    } */


    /************************************************************************/
    /* 每个关节依次进行规划                                               */
    /************************************************************************/
    for (i = 0; i < JNT_NUM; i++)
    {
        Interp3rdPoly_Param_P2P_Zrros_fcn(Angle0[i], Angled[i], tf, a0345);
        Interp3rdPoly_Data_t_P2P_Zrros_fcn(Tx, a0345, qq_data);
        fDesiredJntAngleDeg[i] = qq_data[0];
        fDesiredJntRateDeg[i] = qq_data[1];
        fDesiredJntAccDeg[i] = qq_data[2];
    }

    return;
}

void MultipleJointCartPlan(float Tx,float tf,float init_pos[], float final_pos[], float DesiredPos[],float DesiredVel[],float DesiredAcc[])
{
    float a0345[4], pos_data[3];
    for (int i = 0; i < 7; i++)
    {
        Interp3rdPoly_Param_P2P_Zrros_fcn(init_pos[i], final_pos[i], tf, a0345);
        Interp3rdPoly_Data_t_P2P_Zrros_fcn(Tx, a0345, pos_data);
        DesiredPos[i] = pos_data[0];
        DesiredVel[i] = pos_data[1];
        DesiredAcc[i] = pos_data[2];

    }
    return;
}
    /*	函数名称:	trapezoidal_plan
    函数功能:梯形规划
    参数:
    输入：JNT_NUM：关节个数
      init_pos[]：初始关节角
      final_pos[]：终止关节角
      max_vel：最大速度
      max_acc：最大加速度
      fDesiredJntAngleDeg：输出变量，期望关节角度
    输出：fDesiredJntAngleDeg：期望位置
    返回值:无*/
void trapezoidal_plan(int JNT_NUM, float init_pos[], float final_pos[], double max_vel, double max_acc,float fDesiredJntAngleDeg[])
{
        double distance[JNT_NUM];
        double t_tmp[JNT_NUM];

        double t_f = 0;
        double t_c = 0;
        double t = 0;

        double accel_time = max_vel / max_acc;
        double q_c = 0.5 * max_acc * accel_time * accel_time;
        // double accel_distance = 0.5 * max_acc * accel_time * accel_time;
        t_c = max_vel / max_acc;

        for(int i = 0; i < JNT_NUM; i++)
        {
            distance[i] = abs(final_pos[i] - init_pos[i]);

            t_tmp[i] = (distance[i] - 2 * q_c ) / max_vel + 2 * accel_time;

            if (t_tmp[i] >= t_f)
            {
                t_f = t_tmp[i];
            }
        }

        cout << "t_f: " << t_f <<" " << "accel_time:" << accel_time << endl;

    while (t < t_f)
    {
    
        for(int i = 0; i < JNT_NUM; i++)
        {

        //    t_c = (T - 2 * accel_time) / 2;

           if(t_f - 2 * accel_time > 0)//梯形
           {
                if(t <= accel_time)
                {
                   fDesiredJntAngleDeg[i] = init_pos[i]+0.5 * max_acc * t * t;
                }else if (accel_time < t && t < t_f - t_c)
                {
                   fDesiredJntAngleDeg[i] = init_pos[i]+0.5 * max_acc * t_c * t_c + max_vel* (t-t_c);
                }else{
                     fDesiredJntAngleDeg[i] = init_pos[i] + distance[i]-0.5 * max_acc * (t_f-t) * (t_f-t);
                } 
                
           }else{//未达到最大速度，也即速度曲线为三角形
                t_c = sqrt( (final_pos[i] - init_pos[i]) / max_acc);
                t_f = 2*t_c;
                if(t <= t_c)
                    fDesiredJntAngleDeg[i] = init_pos[i] + 0.5 * max_acc * t * t;
                else 
                    fDesiredJntAngleDeg[i] = distance[i] - 0.5 * max_acc * (t_f-t) * (t_f-t);
           }
        }

        // cout << "time:" <<t<< " desired angle: ";
        for(int i = 0; i < JNT_NUM; i++)
        {
            cout << fDesiredJntAngleDeg[i] << " ";
        }
        cout << std::endl;
        t = t + 0.1;
    }

    return;
}

    /*函数名称:	Rbt_EulerXyz2Tr
    函数功能:根据欧拉角计算旋转变换矩阵
    参数:
    输入：Euler_xyz[6]：输入变量，欧拉角，弧度，数组中的数分别表示x、y、z和绕x、y、z转的角度
    输出：TransMtrx[4][4]：输出变量，旋转后的矩阵
    返回值:无*/
void Rbt_EulerXyz2Tr(float Euler_xyz[], float TransMtrx[][4])
{
    float angle_z = Euler_xyz[5]; // 偏航角
    float angle_y = Euler_xyz[4]; // 俯仰角
    float angle_x = Euler_xyz[3]; // 滚转角

    TransMtrx[0][3] = Euler_xyz[0];
    TransMtrx[1][3] = Euler_xyz[1];
    TransMtrx[2][3] = Euler_xyz[2];

    TransMtrx[0][0] = cos(angle_y) * cos(angle_z);
    TransMtrx[0][1] = cos(angle_z) * sin(angle_x) * sin(angle_y) - cos(angle_x) * sin(angle_z);
    TransMtrx[0][2] = cos(angle_x) * cos(angle_z) * sin(angle_y) + sin(angle_x) * sin(angle_z);

    TransMtrx[1][0] = cos(angle_y) * sin(angle_z);
    TransMtrx[1][1] = cos(angle_x) * cos(angle_z) + sin(angle_x) * sin(angle_y) * sin(angle_z);
    TransMtrx[1][2] = -cos(angle_z) * sin(angle_x) + cos(angle_x) * sin(angle_y) * sin(angle_z);

    TransMtrx[2][0] = -sin(angle_y);
    TransMtrx[2][1] = cos(angle_y) * sin(angle_x);
    TransMtrx[2][2] = cos(angle_x) * cos(angle_y);

    TransMtrx[3][0] = 0.0f;
    TransMtrx[3][1] = 0.0f;
    TransMtrx[3][2] = 0.0f;
    TransMtrx[3][3] = 1.0f;
}

/* void MoveJ(double tf,float start[], float target[])
{
    float Tx = 0;
    float RestrainPara_MaxVel = 9999 * PI / 180;  //最大角速度限制
    float RestrainPara_ACC = 9999 * PI / 180;  //最大角加速度限制
    float fDesiredJntAngleDeg[7], fDesiredJntRateDeg[7], fDesiredJntAccDeg[7];
    while (Tx <= tf+0.001)
    {
        MultipleJointP2PPlan(Tx, DoF, start, target, tf, RestrainPara_MaxVel, RestrainPara_ACC,fDesiredJntAngleDeg, fDesiredJntRateDeg, fDesiredJntAccDeg);
        Tx += 0.001;
    // 期望关节角、角速度、加速度发给驱动器 

    }

    return;
}
 */


/* void MoveL(float Tx, float tf,Eigen::Matrix4f start, Eigen::Matrix4f target,Eigen::Matrix4f &DesiredPos)
{
    float delta_x = target(0,3) - start(0,3);
    float delta_y = target(1,3) - start(1,3);
    float delta_z = target(2,3) - start(2,3);

    DesiredPos.block<3,3>(0,0) = start.block<3,3>(0,0);
    DesiredPos(0,3) = target(0,3) + Tx * delta_x / tf;
    DesiredPos(1,3) = target(1,3) + Tx * delta_y / tf;
    DesiredPos(2,3) = target(2,3) + Tx * delta_z / tf;

    return;
} */

void MoveL(float Tx, float tf,float start[], float target[],float DesiredPos[])
{
    float delta_x = target[0] - start[0];
    float delta_y = target[1] - start[1];
    float delta_z = target[2] - start[2];

    DesiredPos[0] = start[0] + Tx * delta_x / tf;
    DesiredPos[1] = start[1] + Tx * delta_y / tf;
    DesiredPos[2] = start[2] + Tx * delta_z / tf ;

    return;
}

// 计算五次多项式系数
void calculateCoefficients(SShapeTrajectory *traj) {
    double T = traj->T;
    // 五次多项式系数计算
    traj->coeffs[0] = traj->p0;
    traj->coeffs[1] = traj->v0;
    traj->coeffs[2] = 0.5 * traj->a0;
    traj->coeffs[3] = (20 * traj->pf - 20 * traj->p0 - (8 * traj->vf + 12 * traj->v0) * T - (3 * traj->a0 - traj->af) * T * T) / (2 * T * T * T);
    traj->coeffs[4] = (30 * traj->p0 - 30 * traj->pf + (14 * traj->vf + 16 * traj->v0) * T + (3 * traj->a0 - 2 * traj->af) * T * T) / (2 * T * T * T * T);
    traj->coeffs[5] = (12 * traj->pf - 12 * traj->p0 - (6 * traj->vf + 6 * traj->v0) * T - (traj->a0 - traj->af) * T * T) / (2 * T * T * T * T * T);
    return;
}
// 计算位置、速度、加速度
void quinticPolynomialsCalculate(SShapeTrajectory *traj, double t,double output[3]) {
    output[0] = traj->coeffs[0] + traj->coeffs[1] * t + traj->coeffs[2] * t * t + traj->coeffs[3] * t * t * t + traj->coeffs[4] * t * t * t * t + traj->coeffs[5] * t * t * t * t * t;
    output[1] = traj->coeffs[1] + 2 * traj->coeffs[2] * t + 3 * traj->coeffs[3] * t * t + 4 * traj->coeffs[4] * t * t * t + 5 * traj->coeffs[5] * t * t * t * t;
    output[2] = 2 * traj->coeffs[2] + 6 * traj->coeffs[3] * t + 12 * traj->coeffs[4] * t * t + 20 * traj->coeffs[5] * t * t * t;
    return;
}

void quinticPolynomialsPlanner(SShapeTrajectory *traj,float Tx,int JNT_NUM,float tf,float Angle0[], float Angled[],float fDesiredJntAngleDeg[], float fDesiredJntRateDeg[], float fDesiredJntAccDeg[])
{
    double q_data[3];
    traj->T = tf;
    /************************************************************************/
    /* 每个关节依次进行规划                                               */
    /************************************************************************/
    for (int i = 0; i < JNT_NUM; i++)
    {
        traj->p0 = Angle0[i];
        traj->pf = Angled[i];
        calculateCoefficients(traj);
        quinticPolynomialsCalculate(traj, Tx, q_data);
        fDesiredJntAngleDeg[i] = q_data[0];
        fDesiredJntRateDeg[i] = q_data[1];
        fDesiredJntAccDeg[i] = q_data[2];
    }
    return;
}

    //Si = Ai + Bi * (X - Xi) + Ci*(X - Xi)*(X - Xi) + Di * (X - Xi) * (X - Xi) * (X - Xi)
   void CubicSplineCoefficients(Spline* Splines,VectorXd x, VectorXd y)
   {
        int n;
        n = x.size();

/*         // 系数矩阵
        double *h = (double*)malloc((n-1) * sizeof(double));
        double *alpha = (double*)malloc((n-1) * sizeof(double));
        double *l = (double*)malloc(n * sizeof(double));
        double *mu = (double*)malloc((n-1) * sizeof(double));
        double *z = (double*)malloc(n * sizeof(double));

        // 初始化
        for (int i = 0; i < n; i++) {
            l[i] = 1.0;
            z[i] = 0.0;
        }
        
        for (int i = 0; i < n-1; i++) {
            h[i] = x[i+1] - x[i];
            if (h[i] == 0) {
                printf("Error: x[%d] and x[%d] cannot be the same\n", i, i+1);
                return;
            }
        }

        for (int i = 1; i < n-1; i++) {
            alpha[i] = (3.0 / h[i-1]) * (y[i] - y[i-1]) - (3.0 / h[i]) * (y[i+1] - y[i]);
        }

        // 解方程组
        for (int i = 1; i < n-1; i++) {
            l[i] = 2.0 * (x[i+1] - x[i-1]) - h[i-1] * mu[i-1];
            mu[i] = h[i] / l[i];
            z[i] = (alpha[i] - h[i-1] * z[i-1]) / l[i];
        }

        // 反向计算c、b、d
        for (int i = n-2; i >= 0; i--) {
            Splines[i].c = z[i] - mu[i] * Splines[i+1].c;
            Splines[i].b = (y[i+1] - y[i]) / h[i] - h[i] * (Splines[i+1].c + 2.0 * Splines[i].c) / 3.0;
            Splines[i].d = (Splines[i+1].c - Splines[i].c) / (3.0 * h[i]);
            Splines[i].a = y[i];
        }

        // 释放内存
        free(h);
        free(alpha);
        free(l);
        free(mu);
        free(z); */

        vector<double> h, alpha, l, mu, z;
         // 初始化矩阵和向量
        h.resize(n - 1);
        alpha.resize(n - 1);
        l.resize(n);
        mu.resize(n - 1);
        z.resize(n);

        // 计算h和alpha

        for (int i = 0; i < n - 1; ++i) {
            h[i] = x[i + 1] - x[i];
            alpha[i] = (3.0 / h[i]) * (y[i + 1] - y[i]) - (3.0 / h[i - 1]) * (y[i] - y[i - 1]);
        }

        // 设置边界条件
        l[0] = 1;
        mu[0] = 0;
        z[0] = 0;

        // 构造三对角矩阵
        for (int i = 1; i < n - 1; ++i) {
            l[i] = 2 * (x[i + 1] - x[i - 1]) - h[i - 1] * mu[i - 1];
            mu[i] = h[i] / l[i];
            z[i] = (alpha[i] - h[i - 1] * z[i - 1]) / l[i];
        }

        l[n - 1] = 1;
        z[n - 1] = 0;
        // c[n - 1] = 0;
        Splines[n - 1].c = 0;
        // 反向计算c、b、d
        for (int i = n-2; i >= 0; i--) {
            Splines[i].c = z[i] - mu[i] * Splines[i+1].c;
            Splines[i].b = (y[i+1] - y[i]) / h[i] - h[i] * (Splines[i+1].c + 2.0 * Splines[i].c) / 3.0;
            Splines[i].d = (Splines[i+1].c - Splines[i].c) / (3.0 * h[i]);
            Splines[i].a = y[i];
        } 

        return;
   }

    // 计算指定x值的插值
    void SplineEval(Spline* Splines, VectorXd x, double x_val,double* output) {
        int n = x.size();

        int i = 0;
        // 找到对应区间
        while (x_val > x[i+1] && i < n-1) {
            i++;
        }
        // 使用三次多项式公式进行插值
        double dx = x_val - x[i];
        *output = Splines[i].a + Splines[i].b * dx + Splines[i].c * dx * dx + Splines[i].d * dx * dx * dx; 
        return;
    }

   void CubicSplinePlanner(Spline *Splines,float Tx,int JNT_NUM,VectorXd tf,MatrixXd Joint_Desired, VectorXd &Joint_Seg)
    {
        size_t seg = Joint_Desired.cols();
        VectorXd JntPos;
        JntPos.resize(seg);
        // double* val = new double;
        double val[7] ;
        for(size_t i = 0; i < JNT_NUM; i++)
        {
            JntPos = Joint_Desired.row(i);
            CubicSplineCoefficients(Splines,tf,JntPos);
            SplineEval(Splines,tf,Tx,val);
            Joint_Seg[i] = *val;
        }
        // delete val;
        return;
    }


    // 计算指定x值的插值
    void SplineCal(VectorXd x, VectorXd y,double x_val,double* output) {

        int n = x.size();
        int i = 0;
        vector<double> h, alpha, l, mu, z;
        vector<Spline>Splines(n-1);
        vector<double> a,b,c,d;
        a.resize(n-1);
        b.resize(n-1);
        c.resize(n-1);
        d.resize(n-1);
         // 初始化矩阵和向量
        h.resize(n - 1);
        alpha.resize(n - 1);
        l.resize(n);
        mu.resize(n - 1);
        z.resize(n);

        // 计算h和alpha
        for (int i = 0; i < n - 1; ++i) {
            h[i] = x[i + 1] - x[i];
            alpha[i] = (3.0 / h[i]) * (y[i + 1] - y[i]) - (3.0 / h[i - 1]) * (y[i] - y[i - 1]);
        }

        // 设置边界条件
        l[0] = 1;
        mu[0] = 0;
        z[0] = 0;

        // 构造三对角矩阵
        for (int i = 1; i < n - 1; ++i) {
            l[i] = 2 * (x[i + 1] - x[i - 1]) - h[i - 1] * mu[i - 1];
            mu[i] = h[i] / l[i];
            z[i] = (alpha[i] - h[i - 1] * z[i - 1]) / l[i];
        }

        l[n - 1] = 1;
        z[n - 1] = 0;
        // c[n - 1] = 0;
        // Splines[n - 1].c = 0;
        // 反向计算c、b、d
/*         for (int i = n-2; i >= 0; i--) {
            Splines[i].c = z[i] - mu[i] * Splines[i+1].c;
            Splines[i].b = (y[i+1] - y[i]) / h[i] - h[i] * (Splines[i+1].c + 2.0 * Splines[i].c) / 3.0;
            Splines[i].d = (Splines[i+1].c - Splines[i].c) / (3.0 * h[i]);
            Splines[i].a = y[i];
        } */

        for (int i = n-2; i >= 0; i--) {
            c[i] = z[i] - mu[i] * c[i+1];
            b[i] = (y[i+1] - y[i]) / h[i] - h[i] * (c[i+1] + 2.0 * c[i]) / 3.0;
            d[i] = (c[i+1] - c[i]) / (3.0 * h[i]);
            a[i] = y[i];
        }
        // 找到对应区间
        while (x_val > x[i+1] && i < n-1) {
            i++;
        }
        // 使用三次多项式公式进行插值
        double dx = x_val - x[i];
        // *output = Splines[i].a + Splines[i].b * dx + Splines[i].c * dx * dx + Splines[i].d * dx * dx * dx; 
        *output = a[i] + b[i] * dx + c[i] * dx * dx + d[i] * dx * dx * dx;
        
        return;
    }

   void SplinePlanner(float Tx,int JNT_NUM,VectorXd tf,MatrixXd Joint_Desired, VectorXd &Joint_Seg)
    {
        size_t seg = Joint_Desired.cols();
        VectorXd JntPos;
        JntPos.resize(seg);
        // double* val = new double;
        double val[7] ;
        for(size_t i = 0; i < JNT_NUM; i++)
        {
            JntPos = Joint_Desired.row(i);
            SplineCal(tf,JntPos,Tx,val);
            Joint_Seg[i] = *val;
        }
        // delete val;
        return;
    }


robot_control::robot_control(const char* arm_type)
{
    if(strcmp(arm_type,"right") == 0)
    {
        memcpy(DH_OBC,dh_param_r,sizeof(dh_param_r));
    }else if(strcmp(arm_type,"left") == 0)
    {
        memcpy(DH_OBC,dh_param_l,sizeof(dh_param_l));

    }else{
        ROS_ERROR("arm type error");
    }

}

/*************************************************************/
/*  矩阵相乘计算函数: C=AB，C为MxN矩阵，A为MxP，B为PxN
    输入：A[m][p]，B[p][n]
    输出：C[m][n].
*/
/*************************************************************/
void robot_control::Rbt_MulMtrx(int m, int n, int p, float* A, float* B, float* C)
{
    int i, j, k;
    for (i = 0; i < m; i++)
    {
        for (j = 0; j < n; j++)
        {
            C[n * i + j] = 0.0;
            for (k = 0; k < p; k++)
            {
                C[n * i + j] = C[n * i + j] + A[p * i + k] * B[n * k + j];
            }
        }
    }
}
/*************************************************************/
/*	函数名称:	nfCross
    函数功能:	计算矢量u叉乘v
    参    数:u[3]:输入变量，矢量u；
            v[3]:输入变量，矢量v；
            n[3]:输出变量，u叉乘v后的矢量；
    返 回 值:无
*/
/*************************************************************/
void robot_control::nfCross(float u[], float v[], float n[])
{
    n[0] = u[1] * v[2] - u[2] * v[1];
    n[1] = u[2] * v[0] - u[0] * v[2];
    n[2] = u[0] * v[1] - u[1] * v[0];
}

/*************************************************************/
/*矩阵求逆计算函数*/
/*************************************************************/
int robot_control::Rbt_InvMtrx(float* C, float* IC, int n)
{
    int i, j, k, l;
    /* 单位阵*/
    for (i = 0; i < n; i++)
    {
        for (j = 0; j < n; j++)
        {
            *(IC + i * n + j) = 0.0;
        }
        *(IC + i * n + i) = 1.0;
    }
    /* 化上三角阵*/
    for (j = 0; j < n; j++)
    {
        if (fabs(*(C + j * n + j)) > 1e-15) /* C[j][j]不等于0*/
        {
            /* IC阵的第j行除以C[j][j]*/
            for (k = 0; k < n; k++)
            {
                *(IC + j * n + k) /= *(C + j * n + j);
            }
            /* C阵的第j行除以C[j][j]*/
            for (k = n - 1; k >= j; k--)
            {
                *(C + j * n + k) /= *(C + j * n + j);
            }

            for (i = j + 1; i < n; i++)
            {
                /* IC阵的第i行 - C[i][j]*IC阵的第j行*/
                for (k = 0; k < n; k++)
                {
                    *(IC + i * n + k) -= *(C + i * n + j) * *(IC + j * n + k);
                }
                /* C阵的第i行 - C[i][j]*C阵的第j行*/
                for (k = n - 1; k >= j; k--)
                {
                    *(C + i * n + k) -= *(C + i * n + j) * *(C + j * n + k);
                }
            }
        }
        else if (j < n - 1)
        {

            for (l = j + 1; l < n; l++)
            {
                /* 若C阵第j行后的C[l][j]不等于0，第j行加上第l行*/
                if (fabs(*(C + l * n + j)) > 1e-15)
                {
                    for (k = 0; k < n; k++)
                    {
                        *(IC + j * n + k) += *(IC + l * n + k);
                    }
                    for (k = n - 1; k >= j; k--)
                    {
                        *(C + j * n + k) += *(C + l * n + k);
                    }
                    /* IC阵的第j行除以C[j][j]*/
                    for (k = 0; k < n; k++)
                    {
                        *(IC + j * n + k) /= *(C + j * n + j);
                    }
                    /* C阵的第j行除以C[j][j]*/
                    for (k = n - 1; k >= j; k--)
                    {
                        *(C + j * n + k) /= *(C + j * n + j);
                    }
                    /* 第i行 - C[i][j]*第j行*/
                    for (i = j + 1; i < n; i++)
                    {
                        for (k = 0; k < n; k++)
                        {
                            *(IC + i * n + k) -= *(C + i * n + j) * *(IC + j * n + k);
                        }
                        for (k = n - 1; k >= j; k--)
                        {
                            *(C + i * n + k) -= *(C + i * n + j) * *(C + j * n + k);
                        }
                    }
                    break;
                }
            }

            if (l == n)  /* C[l][j] 全等于0*/
            {
                return (-1);   /*  矩阵的行列式为零，不可求逆*/
            }
        }
        else  /* C[n][n]等于0*/
        {
            return (-1);    /*  矩阵的行列式为零，不可求逆*/
        }
    }
    /* 化成单位阵*/
    for (j = n - 1; j >= 1; j--)
    {
        for (i = j - 1; i >= 0; i--)
        {
            for (k = 0; k < n; k++)
            {
                *(IC + i * n + k) -= *(C + i * n + j) * *(IC + j * n + k);
            }
            *(C + i * n + j) = 0;
        }
    }

    return (1);
}
/************************************************************************/
/*		根据DH参数，计算机械臂的Jacobian矩阵，在基坐标系中的表示
        DH=[theta, alpha, a, d]
        输入：float DH_OBC[][4], DH参数表
              floatJointAngle10[],当前绳驱空间关节角
        输出：float dRbtJcb[][JNT_NUM_L],速度雅可比矩阵
              float T0n_c[][4]，当前末端姿态矩阵
*/
/************************************************************************/

void robot_control::Rbt_CalJcb(float DH_OBC[][4], float JointAngle8[], float dRbtJcb[][8], float T0n_c[][4])
{
    float T1[4][4], T2[4][4], Tn[4][4], alpha_sr[JNT_NUM_L], a_sr[JNT_NUM_L], d_sr[JNT_NUM_L], qq[JNT_NUM_L], q0_sr[JNT_NUM_L];
    float zi_1[3], zi_1_All[3][JNT_NUM_L], P_i_1_n[3], P_i_All[3][JNT_NUM_L], Cros_Z_P[3];
    int i, j, k;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        q0_sr[i] = DH_OBC[i][0];
        alpha_sr[i] = DH_OBC[i][3];
        a_sr[i] = DH_OBC[i][2];
        d_sr[i] = DH_OBC[i][1];
    }

    Tn[0][0] = 1;    Tn[0][1] = 0;   Tn[0][2] = 0;  Tn[0][3] = 0;
    Tn[1][0] = 0;    Tn[1][1] = 1;   Tn[1][2] = 0;  Tn[1][3] = 0;
    Tn[2][0] = 0;    Tn[2][1] = 0;   Tn[2][2] = 1;  Tn[2][3] = 0;
    Tn[3][0] = 0;    Tn[3][1] = 0;   Tn[3][2] = 0;  Tn[3][3] = 1;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        for (j = 0;j < 3;j++)
        {
            zi_1_All[j][i] = Tn[j][2];			//Tn[j][2]为z向量
        }

        qq[i] = q0_sr[i] + JointAngle8[i];
        T1[0][0] = (float)(cos(qq[i]));                    T1[0][1] = -(float)(cos(alpha_sr[i]) * sin(qq[i]));
        T1[0][2] = (float)(sin(alpha_sr[i]) * sin(qq[i]));   T1[0][3] = (float)(a_sr[i] * cos(qq[i]));

        T1[1][0] = (float)(sin(qq[i]));                    T1[1][1] = (float)(cos(alpha_sr[i]) * cos(qq[i]));
        T1[1][2] = -(float)(sin(alpha_sr[i]) * cos(qq[i]));   T1[1][3] = (float)(a_sr[i] * sin(qq[i]));
        T1[2][0] = 0;  T1[2][1] = (float)(sin(alpha_sr[i])); T1[2][2] = (float)(cos(alpha_sr[i]));   T1[2][3] = d_sr[i];
        T1[3][0] = 0;  T1[3][1] = 0; T1[3][2] = 0;   T1[3][3] = 1;
        Rbt_MulMtrx(4, 4, 4, Tn[0], T1[0], T2[0]);
        for (j = 0;j < 4;j++)
        {
            for (k = 0;k < 4;k++)
            {
                Tn[j][k] = T2[j][k];				//正运动学
                /*	Trans_Matrix[j][4*i+k] = T6[j][k];*/
            }
        }
        for (j = 0;j < 3;j++) P_i_All[j][i] = Tn[j][3]; //Tn[j][3]为p向量
    }
    for (j = 0;j < 3;j++)
    {
        P_i_1_n[j] = P_i_All[j][JNT_NUM_L - 1];  /*P_0_n*/
    }
    for (i = 0; i < JNT_NUM_L; i++)
    {
        for (j = 0;j < 3;j++)
        {
            zi_1[j] = zi_1_All[j][i];
        }
        nfCross(zi_1, P_i_1_n, Cros_Z_P); //矢量积
        for (j = 0;j < 3;j++)
        {
            dRbtJcb[j][i] = Cros_Z_P[j];
            dRbtJcb[j + 3][i] = zi_1[j];
            P_i_1_n[j] = P_i_All[j][JNT_NUM_L - 1] - P_i_All[j][i];  /*P_0_n*/
        }
    }
    for (i = 0;i < 4;i++)
    {
        for (j = 0;j < 4;j++)
        {
            T0n_c[i][j] = Tn[i][j];   /*---返回当前的末端位姿矩阵--*/
        }
    }
}

/*************************************************************/
/*---计算广义逆矩阵， 6X7， m《=n---  Moore-penrose广义逆
  ----A_pinv = A'*(A*A')^(-1)---
  */
/*************************************************************/
void robot_control::Rbt_PInvMtrx67(float AA[][7], float AA_pinv[][6])
{
    int i, j;
    float AA_T[7][6], BB[6][6], BB_Inv[6][6];

    for (i = 0; i < 7; i++)
    {
        for (j = 0; j < 6; j++)
        {
            AA_T[i][j] = AA[j][i];
        }
    }

    Rbt_MulMtrx(6, 6, 7, AA[0], AA_T[0], BB[0]); /*---BB = AA*AA_T----*/

    for (j = 0; j < 6; j++)
    {
        BB[j][j] = BB[j][j]+0.001;
    }

    Rbt_InvMtrx(BB[0], BB_Inv[0], 6);
    Rbt_MulMtrx(7, 6, 6, AA_T[0], BB_Inv[0], AA_pinv[0]); /*---BB = AA*AA_T----*/
}

/************************************************************************/
/*-----	数值解求解机械臂逆运动学
    功能：根据期望末端矩阵和当前关节角求解期望关节角
    输入：float DH_OBC[][4]DH参数
          float T0n[][4]期望末端位姿矩阵
          float JntCurrent10[]当前关节角
    输出：float JntCalcted10[]计算期望关节角
*/
/************************************************************************/
int robot_control::nfInvKinePosLev(float T0n[][4], float JntCurrent8[], float JntCalcted8[])
{
    float DH_q0[JNT_NUM_L], DH_alpha[JNT_NUM_L], DH_a[JNT_NUM_L], DH_d[JNT_NUM_L];
    float T0n_c[4][4], dA[6], AttErrVec[3];
    float Jcb_0[6][JNT_NUM_L], Jcb_0_pinv[JNT_NUM_L][6], Jcb_0_simple[6][7];
    float q_Itera8[JNT_NUM_L], dq[7], dp_norm, do_norm;  /*---用于迭代的中间变量---*/

    float efs,efs_o;
    int solutionFlag;
    float n[3], o[3], a[3], nd[3], od[3], ad[3];
    float Cros1[3], Cros2[3], Cros3[3];

    /*简化雅可比的系数矩阵 4和5关节耦合*/
    float u[JNT_NUM_L][7] =
        {
            {1.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		1.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		1.0f,		0.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		1.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		1.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		0.0f,		1.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		1.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		1.0f}     
        };

    int i, lmax, l_limit;
    lmax = 0;
    l_limit = 15000;
    dp_norm = 1;
    do_norm = 1;
    efs = (float)(1e-6);//1.0e-6
    efs_o = (float)(1e-6);//1.0e-6
    solutionFlag = 0;
    float d_Jnt[10]={};
    for (i = 0; i < JNT_NUM_L; i++)
    {
        DH_q0[i] = DH_OBC[i][0];
        DH_alpha[i] = DH_OBC[i][3];
        DH_a[i] = DH_OBC[i][2];
        DH_d[i] = DH_OBC[i][1];

        q_Itera8[i] = JntCurrent8[i];
    }

    for (i = 0; i < 3; i++)
    {

        nd[i] = T0n[i][0];
        od[i] = T0n[i][1];
        ad[i] = T0n[i][2];
    }

    while (lmax <= l_limit)
    {
        //        if ((dp_norm > efs) || (do_norm > efs_o)||(d_Jnt[0]>0.1045329f)||(d_Jnt[2]>0.2045329f)||(d_Jnt[3]>0.4045329f)||(d_Jnt[4]>0.1045329f)||(d_Jnt[5]>0.5045329f)||(d_Jnt[6]>0.1045329f)) //这里限制了关节单次不得超过10度
        if ((dp_norm > efs) || (do_norm > efs_o)) //这里限制了关节单次不得超过10度
        {
            solutionFlag = 0;

            // Rbt_CalJcb(DH_OBC, q_Itera11, Jcb_0, T0n_c);
            Rbt_CalJcb(DH_OBC, q_Itera8, Jcb_0, T0n_c);

            for (i = 0; i < 3; i++)
            {
                n[i] = T0n_c[i][0];
                o[i] = T0n_c[i][1];
                a[i] = T0n_c[i][2];
            }

            nfCross(n, nd, Cros1);
            nfCross(o, od, Cros2);
            nfCross(a, ad, Cros3);

            for (i = 0; i < 3; i++)
            {
                AttErrVec[i] = 0.5f * (Cros1[i] + Cros2[i] + Cros3[i]); //---姿态误差
                dA[i] = T0n[i][3] - T0n_c[i][3];                        //位置误差
                dA[i + 3] = AttErrVec[i];                               //姿态误差
            }

            Rbt_MulMtrx(6, 7, JNT_NUM_L, Jcb_0[0], u[0], Jcb_0_simple[0]);

            Rbt_PInvMtrx67(Jcb_0_simple, Jcb_0_pinv);                   //Moore-penrose广义逆

            Rbt_MulMtrx(7, 1, 6, Jcb_0_pinv[0], dA, dq);                // ---ARM_b用于平衡质心位置和姿态

            dp_norm = 0;
            do_norm = 0;
            for (i = 0; i < 3; i++)
            {
                dp_norm = dp_norm + dA[i] * dA[i];
                do_norm = do_norm + dA[i + 3] * dA[i + 3];//误差的模的平方
            }

            q_Itera8[0] = q_Itera8[0] + dq[0];
            q_Itera8[1] = q_Itera8[1] + dq[1];
            q_Itera8[2] = q_Itera8[2] + dq[2];
            q_Itera8[3] = q_Itera8[3] + dq[3];
            q_Itera8[4] = q_Itera8[4] + dq[3];
            q_Itera8[5] = q_Itera8[5] + dq[4];
            q_Itera8[6] = q_Itera8[6] + dq[5];
            q_Itera8[7] = q_Itera8[7] + dq[6];

            /************************************************************************/
            /*  关节角范围    还需要进一步确认                                                 */
            /************************************************************************/
            float q_limit_low_l[] = {-45*3.1415926f/180,-90*3.1415926f/180,0*3.1415926f/180,-45*3.1415926f/180,-45*3.1415926f/180,-60*3.1415926f/180,-60*3.1415926f/180,-90*3.1415926f/180};
            float q_limit_high_l[] = {70*3.1415926f/180,90*3.1415926f/180,90*3.1415926f/180,80*3.1415926f/180,80*3.1415926f/180,60*3.1415926f/180,60*3.1415926f/180,90*3.1415926f/180};

            for (int i = 0;i<JNT_NUM_L;i++) {

                //                cout<<"i="<<lmax<<" ; "<<q_Itera10[i]<<endl;

                if (q_Itera8[i]<q_limit_low_l[i])
                {
                    q_Itera8[i]=q_limit_low_l[i];

                }
                else if (q_Itera8[i]>q_limit_high_l[i])
                {

                    q_Itera8[i]=q_limit_high_l[i];

                }
            }

            lmax = lmax + 1;  /*---更新迭代次数---*/

        }
        else
        {
            solutionFlag = 1;   /*---求解成功---*/

            lmax = l_limit + 111;  /*---求解成功，直接退出---*/
        }

    }

    if (solutionFlag == 1)
    {
        /************************************************************************/
        /*  限制到范围[-pi, pi]---                                                    */
        /************************************************************************/
        for (i = 0;i < JNT_NUM_L;i++)
        {
            while (q_Itera8[i] > 3.14171592654) q_Itera8[i] = q_Itera8[i] - 6.283185307f;
            while (q_Itera8[i] < -3.14171592654) q_Itera8[i] = q_Itera8[i] + 6.283185307f;
        }
        for (i = 0; i < JNT_NUM_L; i++)
        {
            JntCalcted8[i] = q_Itera8[i];
        }
    }
    else
    {
        for (i = 0; i < JNT_NUM_L; i++)
        {
            // JntCalcted11[i] = JntCurrent11[i];
            JntCalcted8[i] = JntCurrent8[i];
        }
    }
    return solutionFlag;
}

/*************************************************************/
/*	函数名称:	nfFkineSpaceRbt
    函数功能:	（关节空间位置级正运动学）根据机械臂关节角计算空间机器人末端工具坐标相对于机械臂安装坐标系的位姿
    参   数: JntCurrent10[]:输入变量，机械臂关节角，弧度
             T0n:输出变量，机械臂末端相对于机械臂安装坐标系的齐次矩阵
    返回值:无
*/
/*************************************************************/
void robot_control::nfFkineSpaceRbt(float JntCurrent8[],float T0n[][4])
{

    float T1[4][4], T2[4][4], alpha_sr[JNT_NUM_L], a_sr[JNT_NUM_L], d_sr[JNT_NUM_L], qq[JNT_NUM_L], q0_sr[JNT_NUM_L];//, Tr0[4][4];
    int i, j, k;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        q0_sr[i] = DH_OBC[i][0];
        alpha_sr[i] = DH_OBC[i][3];
        a_sr[i] = DH_OBC[i][2];
        d_sr[i] = DH_OBC[i][1];
    }

    T0n[0][0] = 1;    T0n[0][1] = 0;   T0n[0][2] = 0;  T0n[0][3] = 0;
    T0n[1][0] = 0;    T0n[1][1] = 1;   T0n[1][2] = 0;  T0n[1][3] = 0;
    T0n[2][0] = 0;    T0n[2][1] = 0;   T0n[2][2] = 1;  T0n[2][3] = 0;
    T0n[3][0] = 0;    T0n[3][1] = 0;   T0n[3][2] = 0;  T0n[3][3] = 1;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        qq[i] = q0_sr[i] + JntCurrent8[i];
        T1[0][0] = (float)cos(qq[i]);                    T1[0][1] = -(float)(cos(alpha_sr[i]) * sin(qq[i]));
        T1[0][2] = (float)(sin(alpha_sr[i]) * sin(qq[i]));   T1[0][3] = (float)(a_sr[i] * cos(qq[i]));

        T1[1][0] = (float)(sin(qq[i]));                    T1[1][1] = (float)(cos(alpha_sr[i]) * cos(qq[i]));
        T1[1][2] = -(float)(sin(alpha_sr[i]) * cos(qq[i]));   T1[1][3] = (float)(a_sr[i] * sin(qq[i]));

        T1[2][0] = 0;  T1[2][1] = (float)(sin(alpha_sr[i])); T1[2][2] = (float)(cos(alpha_sr[i]));   T1[2][3] = d_sr[i];

        T1[3][0] = 0;  T1[3][1] = 0; T1[3][2] = 0;   T1[3][3] = 1;

        Rbt_MulMtrx(4, 4, 4, T0n[0], T1[0], T2[0]);

        for (j = 0;j < 4;j++)
        {
            for (k = 0;k < 4;k++)
            {
                T0n[j][k] = T2[j][k];
            }
        }
    }
}

/**
 * @brief 计算单个关节的DH变换矩阵
 * @param q 关节角度（弧度）
 * @param alpha 扭角（弧度）
 * @param a 连杆长度
 * @param d 连杆偏距
 * @return 4x4变换矩阵
 */
Matrix4 robot_control::dhTransform(float q, float alpha, float a, float d) 
{
    Matrix4 T;
    T << cos(q), -cos(alpha)*sin(q),  sin(alpha)*sin(q), a*cos(q),
            sin(q),  cos(alpha)*cos(q), -sin(alpha)*cos(q), a*sin(q),
            0,       sin(alpha),         cos(alpha),         d,
            0,       0,                  0,                  1;
    return T;
}

/**
 * @brief 正运动学计算：根据关节角度计算末端位姿
 * @param q 关节角度向量
 * @return 末端相对于基坐标系的变换矩阵
 */
Matrix4 robot_control::forwardKinematics(const VectorXf q) 
{
    Matrix4 T_total = Matrix4::Identity();  // 初始化为单位矩阵
    Eigen::MatrixXf DH = Eigen::Map<Eigen::Matrix<float, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>>(
    &DH_OBC[0][0],  // 数组首地址
    8,        // 行数
    4         // 列数
);
    // 遍历所有关节，累积变换矩阵
    for (int i = 0; i < 8; ++i) {
        // 从DH参数表获取参数，q = 输入关节角 + 偏移量
        float q_i = q(i) + DH(i, 0);  
        float alpha_i = DH(i, 3);
        float a_i = DH(i, 2);
        float d_i = DH(i, 1);
        
        // 计算当前关节的变换矩阵并累积
        T_total *= dhTransform(q_i, alpha_i, a_i, d_i);
    }
    return T_total;
}

int robot_control::pinv(Jacobian jcb, Eigen::MatrixXd &dest_mat, double tolerance)
{
/*
	std::vector<std::vector<float>> vec{ { 0.68f, 0.597f, -0.211f },
					{ 0.823f, 0.566f, -0.605f } };
	const int rows{ 2 }, cols{ 3 };

	std::vector<float> vec_;
	for (int i = 0; i < rows; ++i) {
		vec_.insert(vec_.begin() + i * cols, vec[i].begin(), vec[i].end());
	}
	Eigen::Map<Eigen::Matrix<float, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>> m(source_mat.data(), 6, 7);
*/

	//Eigen::Map<Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>> source_mat(jcb.data, 6, 7);
	Eigen::MatrixXd source_mat;
	source_mat = jcb.data;
	//fprintf(stderr, "source matrix:\n");
	//std::cout << source_mat << std::endl;

	//fprintf(stderr, "\nEigen implement pseudoinverse:\n");
	auto svd = source_mat.jacobiSvd(Eigen::ComputeFullU | Eigen::ComputeFullV);

	const auto &singularValues = svd.singularValues();
	Eigen::Matrix<double, Eigen::Dynamic, Eigen::Dynamic> singularValuesInv(source_mat.cols(), source_mat.rows());
	singularValuesInv.setZero();
	//double  pinvtoler = 1.e-6; // choose your tolerance wisely
	for (unsigned int i = 0; i < singularValues.size(); ++i) {
		if (singularValues(i) > tolerance)
			singularValuesInv(i, i) = 1.0f / singularValues(i);
		else
			singularValuesInv(i, i) = 0.f;
	}

	Eigen::MatrixXd pinvmat = svd.matrixV() * singularValuesInv * svd.matrixU().transpose();
	//std::cout << pinvmat << std::endl;
	dest_mat = pinvmat;

	return 0;
}

void robot_control::jointStateCallback(const sensor_msgs::JointState::ConstPtr &msg)
{
    jntSubFlag = true;
    //   q_cur = msg->position;
    for(int i = 0; i < 8; i++)
    {
            left_arm_pos[i] = msg->position[i];
            right_arm_pos[i] = msg->position[i+8];
            // cout << "joint_pos: " << left_init_pos[i]<<" ";
        
    }
    //   cout << endl;
    return;
}

void robot_control::getJointPos(ros::NodeHandle& nh,VectorXf& jointpos)
{
    joint_sub = nh.subscribe("/motor_state/arm_motor_state_actual", 1000, &robot_control::jointStateCallback,this);
    while(!jntSubFlag && ros::ok())
    {
        ROS_INFO("waiting for joint position");
        usleep(1000);
        ros::spinOnce();
    }
    for(int i = 0; i < 8; i++)
    {
        jointpos(i) = left_arm_pos[i];
        jointpos(i + 8) = right_arm_pos[i];
    }
    
    return ;
}

void robot_control::pub2Rviz( ros::NodeHandle& nh,float tf,VectorXf jointpos,VectorXf joint_desired)
{
    // cout << "joint_desired:" << endl;
    // for(size_t i = 0; i < joint_desired.size(); i++)
    // {
    //     cout << joint_desired(i) << endl;
    // }
    ros::Publisher pub_rviz = nh.advertise<sensor_msgs::JointState>("joint_states",1);
    sensor_msgs::JointState jnt_state_msgs;

    ros::Rate loop_rate(1000);
    float Tx = 0.0;
    unsigned int nj = 7; 
    float left_joint_pos[nj] = {0.0};
    float right_joint_pos[nj] = {0.0};
    float left_final_pos[nj] = {0.0};
    float right_final_pos[nj] = {0.0};
    float fDesiredJntAngleDeg_l[nj];
    float fDesiredJntRateDeg_l[nj];
    float fDesiredJntAccDeg_l[nj];
    float fDesiredJntAngleDeg_r[nj];
    float fDesiredJntRateDeg_r[nj];
    float fDesiredJntAccDeg_r[nj];
    for(int i = 0; i < nj;i++)
    {
        left_joint_pos[i] = jointpos(i);
        left_final_pos[i] = joint_desired(i);
        right_joint_pos[i] = jointpos(i + nj);
        right_final_pos[i] = joint_desired(i + nj);
    }
    // SShapeTrajectory base;
    // auto traj = make_shared<SShapeTrajectory>(base);
    // shared_ptr<SShapeTrajectory>traj(new SShapeTrajectory());
    // SShapeTrajectory* traj = new SShapeTrajectory;
    while(ros::ok()){
        /* 生成轨迹*/
        MultipleJointP2PPlan(Tx,nj,tf,left_joint_pos,left_final_pos,fDesiredJntAngleDeg_l,fDesiredJntRateDeg_l,fDesiredJntAccDeg_l);
        MultipleJointP2PPlan(Tx,nj,tf,right_joint_pos,right_final_pos,fDesiredJntAngleDeg_r,fDesiredJntRateDeg_r,fDesiredJntAccDeg_r);
        /* 五次生成轨迹*/
        // quinticPolynomialsPlanner(traj,Tx,nj,tf,left_joint_pos,left_final_pos,fDesiredJntAngleDeg_l,fDesiredJntRateDeg_l,fDesiredJntAccDeg_l);
        // quinticPolynomialsPlanner(traj,Tx,nj,tf,right_joint_pos,right_final_pos,fDesiredJntAngleDeg_r,fDesiredJntRateDeg_r,fDesiredJntAccDeg_r);
        jnt_state_msgs.name.resize(14);
        jnt_state_msgs.position.resize(14);
        jnt_state_msgs.header.stamp = ros::Time::now();
        jnt_state_msgs.header.frame_id = "pub2rviz";

        for(int j = 0; j < 4; j++){
            jnt_state_msgs.name[j] = "l_joint" + to_string(j+1);
            jnt_state_msgs.position[j] = fDesiredJntAngleDeg_l[j];
            jnt_state_msgs.name[j+nj] = "r_joint" + to_string(j+1);
            jnt_state_msgs.position[j+nj] = fDesiredJntAngleDeg_r[j];
            // ROS_INFO("%s: %.4f",jnt_state_msgs.name[j].c_str(),jnt_state_msgs.position[j]);
        }
        for(int j = 4; j < 7; j++){
            jnt_state_msgs.name[j] = "l_joint" + to_string(j+2);
            jnt_state_msgs.name[j+nj] = "r_joint" + to_string(j+2);
            jnt_state_msgs.position[j] = fDesiredJntAngleDeg_l[j];
            jnt_state_msgs.position[j+nj] = fDesiredJntAngleDeg_r[j];

        }
/* 6 7关节对调 */
        // jnt_state_msgs.position[5] = fDesiredJntAngleDeg_l[4];
        // jnt_state_msgs.position[4] = fDesiredJntAngleDeg_l[5];
        // jnt_state_msgs.position[12] = fDesiredJntAngleDeg_r[4];
        // jnt_state_msgs.position[11] = fDesiredJntAngleDeg_r[5];

        // cout << endl;
        pub_rviz.publish(jnt_state_msgs);

        Tx = Tx + 0.001;

/*         count++;
        cout << "count:"<< count <<endl; */

        if(Tx > tf)
        {
            // ROS_INFO("position_desired is arrvied");
            break;
        }
        // ros::spinOnce();
        loop_rate.sleep();

    }
    // free(traj);
    return;
}


void robot_control::Jnt8to7(float Jnt8[], Vector7f& Jnt7)
{
    Jnt7(0) = Jnt8[0];
    Jnt7(1) = Jnt8[1];
    Jnt7(2) = Jnt8[2];
    Jnt7(3) = Jnt8[3];
    Jnt7(4) = Jnt8[5];
    Jnt7(5) = Jnt8[6];
    Jnt7(6) = Jnt8[7];
    return;
}

/* jointpos[0]- jointpos[7]左臂 
    jointpos[8] - jointpos[15]右臂*/
void robot_control::move( ros::NodeHandle& nh,float tf,VectorXf jointpos,VectorXf joint_desired)
{
   
    pos_pub = nh.advertise<std_msgs::Float64MultiArray>("motor_command/arm_position_control", 1);
    std_msgs::Float64MultiArray msg;
    msg.data.resize(16);
    ros::Rate loop_rate(50);
    float Tx = 0.0;
    unsigned int nj = 8; 
    float left_joint_pos[nj] = {0.0};
    float right_joint_pos[nj] = {0.0};
    float left_final_pos[nj] = {0.0};
    float right_final_pos[nj] = {0.0};
    float fDesiredJntAngleDeg_l[nj];
    float fDesiredJntRateDeg_l[nj];
    float fDesiredJntAccDeg_l[nj];
    float fDesiredJntAngleDeg_r[nj];
    float fDesiredJntRateDeg_r[nj];
    float fDesiredJntAccDeg_r[nj];
    for(int i = 0; i < nj;i++)
    {
        left_joint_pos[i] = jointpos(i);
        left_final_pos[i] = joint_desired(i);
        right_joint_pos[i] = jointpos(i + nj);
        right_final_pos[i] = joint_desired(i + nj);
    }
    // SShapeTrajectory* traj = new SShapeTrajectory();
    // auto traj = make_shared<SShapeTrajectory>();
    while(ros::ok()){

        /* 三次生成轨迹*/
        MultipleJointP2PPlan(Tx,nj,tf,left_joint_pos,left_final_pos,fDesiredJntAngleDeg_l,fDesiredJntRateDeg_l,fDesiredJntAccDeg_l);
        MultipleJointP2PPlan(Tx,nj,tf,right_joint_pos,right_final_pos,fDesiredJntAngleDeg_r,fDesiredJntRateDeg_r,fDesiredJntAccDeg_r);

        /* 发布轨迹*/
        for(unsigned int j = 0; j < nj;j++)
        {

            msg.data[j] = fDesiredJntAngleDeg_l[j];
            msg.data[j+nj] = fDesiredJntAngleDeg_r[j];
            cout <<fDesiredJntAngleDeg_l[j] * Rad2Deg<< "   ";
        } 
        cout << endl;

        /* 右臂 */
        // msg.data[9] = -msg.data[9];
        // msg.data[14] = -msg.data[14];
        // msg.data[15] = -msg.data[15];

        /* 左臂 */
        msg.data[0] = -msg.data[0];
        msg.data[1] = -msg.data[1];
        pos_pub.publish(msg);

        Tx = Tx + 0.05;

/*         count++;
        cout << "count:"<< count <<endl; */

        if(Tx > tf)
        {
            ROS_INFO("position_desired is arrvied");
            break;
        }
        // ros::spinOnce();
        loop_rate.sleep();

    }

    return ;
}

robot_control::robot_control(const char* urdf_path,const char* arm_type)
{

    Tree my_tree; 
	kdl_parser::treeFromFile(urdf_path,my_tree); 
	bool exit_value; 
    if(strcmp(arm_type,"right") == 0)
    {
	    exit_value = my_tree.getChain("base_0","r_Link8",chain); 
    }else{
        exit_value = my_tree.getChain("base_0","l_Link8",chain); 
    }
	if(!exit_value){ 
		cout << "Error:could not find chain Arm"<<endl; 
	} 

}

// 修正联动关节角度（关节4和5保持一致）
void robot_control::enforceJointCoupling(KDL::JntArray& q) 
{
    int joint4_idx = 3;
    int joint5_idx = 4;
    q(joint5_idx) = q(joint4_idx);
}

// 计算末端误差
double robot_control::calculateError(const KDL::JntArray& q, const KDL::Frame& target) 
{
    KDL::ChainFkSolverPos_recursive fksolver(chain);
    KDL::Frame current;
    fksolver.JntToCart(q, current);
    
    // 计算位置误差（米）
    KDL::Vector pos_err = current.p - target.p;
    double pos_error = pos_err.Norm();
    
    // 计算姿态误差（弧度）
    KDL::Rotation rot_err = current.M * target.M.Inverse();
    double x, y, z;
    rot_err.GetRPY(x, y, z);
    double rot_error = sqrt(x*x + y*y + z*z);
    
    return pos_error + rot_error; // 综合误差
}
int robot_control::invkinematics(JntArray jointpos,Frame target,JntArray& joint_desired)
{
    double eps = 1e-6;
	double eps_joints = 1e-15;
	int maxiter = 100;
    int result; 
    unsigned int nj = chain.getNrOfJoints(); 
	ChainIkSolverPos_LMA iksolver(chain,eps,maxiter,eps_joints);
	result = iksolver.CartToJnt(jointpos, target, joint_desired);
    if (result != 0) {
        std::cerr << "IK solver failed with error code: " << result << std::endl;
        return -1;
    }
    // 应用联动约束
    enforceJointCoupling(joint_desired);
    
    // 检查误差，若过大则进行微调
    double error = calculateError(joint_desired, target);
    const double max_allowed_error = 0.01; // 1厘米位置误差 + 0.01弧度姿态误差
    if (error > max_allowed_error) {
        std::cout << "Error after coupling: " << error << " > " << max_allowed_error << ", trying to adjust..." << std::endl;
        
        // 微调关节4（带动关节5一起变化）
        KDL::JntArray q_adjust = joint_desired;
        double step = 0.01; // 微调步长（弧度）
        double best_error = error;
        KDL::JntArray best_q = joint_desired;
        
        // 搜索附近角度
        for (int i = -10; i <= 10; ++i) {
            q_adjust(3) = joint_desired(3) + i * step; 
            q_adjust(4) = q_adjust(3); 
            
            double current_error = calculateError(q_adjust, target);
            if (current_error < best_error) {
                best_error = current_error;
                best_q = q_adjust;
            }
        }
        
        if (best_error < error) {
            joint_desired = best_q;
            error = best_error;
            std::cout << "Adjusted error: " << error << std::endl;
        } else {
            std::cerr << "无法通过微调减小误差，可能目标位姿超出工作空间" << std::endl;
            return -1;
        }
    }
    std::cout << "求解结果（关节角度，弧度）：" << std::endl;
    for (int i = 0; i < chain.getNrOfJoints(); ++i) {
        std::cout << "关节 " << i+1 << ": " << joint_desired(i) << std::endl;
    }
    std::cout << "关节4和5联动验证：" << (joint_desired(3) == joint_desired(4) ? "一致" : "不一致") << std::endl;
    std::cout << "最终误差：" << error << std::endl; 

    return 0;
}