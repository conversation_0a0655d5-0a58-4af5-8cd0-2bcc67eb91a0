#include <iostream>
#include <Eigen/Dense>
#include <cmath>

// 定义数学常量
#define PI 3.14159265358979323846
#define EPS 1e-6               // 收敛精度
#define MAX_ITER 1000          // 最大迭代次数
#define DAMPING_FACTOR 0.1     // 阻尼系数，提高稳定性

// 类型定义，简化代码
typedef Eigen::Matrix4f Matrix4;          // 4x4变换矩阵
typedef Eigen::Matrix<float, 6, 1> Vector6;  // 6维向量（位置+姿态误差）
typedef Eigen::Matrix<float, 7, 1> Vector7;  // 7维关节角度向量
typedef Eigen::Matrix<float, 6, 7> Jacobian; // 6x7雅克比矩阵
typedef Eigen::Matrix<float, 6, 6> Matrix6f; // 6x7雅克比矩阵

class IKSolver {
private:
    // DH参数表 [alpha, a, d, q_offset]
    Eigen::MatrixXf DH;
    
    // 机械臂结构参数（从DH参数中提取的连杆长度）
    float l0, l1, l2, l3, l4;

public:
    // 构造函数：初始化DH参数
    IKSolver() {
        // 初始化DH参数矩阵 (7关节 x 4参数)
        DH.resize(7, 4);
        
        // 填充DH参数 [alpha, a, d, q_offset]
        DH << -PI/2, 0,    l0=0.2536, -PI/2,
               PI/2, 0,    0,        PI/2,
              -PI/2, 0,    l1=0.2486, PI/2,
               PI/2, 0,    0,        0,
              -PI/2, 0,    l2=0.1745, 0,
              -PI/2, l3=0.07, 0,    -PI/2,
                 0,  l4=0.08, 0,    0;
    }

    /**
     * @brief 计算单个关节的DH变换矩阵
     * @param q 关节角度（弧度）
     * @param alpha 扭角（弧度）
     * @param a 连杆长度
     * @param d 连杆偏距
     * @return 4x4变换矩阵
     */
    Matrix4 dhTransform(float q, float alpha, float a, float d) {
        Matrix4 T;
        T << cos(q), -cos(alpha)*sin(q),  sin(alpha)*sin(q), a*cos(q),
             sin(q),  cos(alpha)*cos(q), -sin(alpha)*cos(q), a*sin(q),
             0,       sin(alpha),         cos(alpha),         d,
             0,       0,                  0,                  1;
        return T;
    }

    /**
     * @brief 正运动学计算：根据关节角度计算末端位姿
     * @param q 关节角度向量
     * @return 末端相对于基坐标系的变换矩阵
     */
    Matrix4 forwardKinematics(const Vector7& q) {
        Matrix4 T_total = Matrix4::Identity();  // 初始化为单位矩阵
        
        // 遍历所有关节，累积变换矩阵
        for (int i = 0; i < 7; ++i) {
            // 从DH参数表获取参数，q = 输入关节角 + 偏移量
            float q_i = q(i) + DH(i, 3);  // DH(i,3)是q_offset
            float alpha_i = DH(i, 0);
            float a_i = DH(i, 1);
            float d_i = DH(i, 2);
            
            // 计算当前关节的变换矩阵并累积
            T_total *= dhTransform(q_i, alpha_i, a_i, d_i);
        }
        return T_total;
    }

    /**
     * @brief 计算雅克比矩阵
     * @param q 关节角度向量
     * @return 6x7雅克比矩阵
     */
    Jacobian computeJacobian(const Vector7& q) {
        Jacobian J = Jacobian::Zero();  // 初始化雅克比矩阵为0
        Matrix4 T_i = Matrix4::Identity();  // 关节i到基坐标系的变换
        
        // 遍历每个关节计算雅克比列
        for (int i = 0; i < 7; ++i) {
            // 计算关节i的变换矩阵
            float q_i = q(i) + DH(i, 3);
            float alpha_i = DH(i, 0);
            float a_i = DH(i, 1);
            float d_i = DH(i, 2);
            Matrix4 T_joint = dhTransform(q_i, alpha_i, a_i, d_i);
            T_i *= T_joint;  // 更新到当前关节的累积变换
            
            // 提取关节i的Z轴单位向量（相对于基坐标系）
            Eigen::Vector3f z_i = T_i.block<3,1>(0,2);  // 前3行，第2列
            
            // 提取末端到关节i的位置向量（相对于基坐标系）
            Eigen::Vector3f p_e = forwardKinematics(q).block<3,1>(0,3);  // 末端位置
            Eigen::Vector3f p_i = T_i.block<3,1>(0,3);  // 关节i位置
            Eigen::Vector3f r_ie = p_e - p_i;  // 末端到关节i的向量
            
            // 计算线速度分量（z_i × r_ie）
            Eigen::Vector3f linear = z_i.cross(r_ie);
            
            // 角速度分量（对于旋转关节，就是z_i）
            Eigen::Vector3f angular = z_i;
            
            // 填充雅克比矩阵的第i列
            J.block<3,1>(0,i) = linear;   // 前3行是线速度
            J.block<3,1>(3,i) = angular;  // 后3行是角速度
        }
        return J;
    }

    /**
     * @brief 计算末端位姿误差
     * @param current 当前末端位姿
     * @param target 目标末端位姿
     * @return 6维误差向量 [dx, dy, dz, rx, ry, rz]
     */
    Vector6 calculateError(const Matrix4& current, const Matrix4& target) {
        Vector6 error;
        
        // 位置误差（米）
        Eigen::Vector3f pos_current = current.block<3,1>(0,3);
        Eigen::Vector3f pos_target = target.block<3,1>(0,3);
        error.head(3) = pos_target - pos_current;
        
        // 姿态误差（使用旋转矩阵的轴角表示）
        Eigen::Matrix3f rot_current = current.block<3,3>(0,0);
        Eigen::Matrix3f rot_target = target.block<3,3>(0,0);
        Eigen::Matrix3f rot_error = rot_target * rot_current.transpose();
        
        // 从旋转矩阵提取误差角度（轴角形式）
        /*         float trace = rot_error.trace();
        float theta = acos(std::max(-1.0f, std::min(1.0f, (trace - 1.0f) / 2.0f)));
        
        if (theta > EPS) {
            float s = sin(theta);
            error(3) = (rot_error(2,1) - rot_error(1,2)) / (2*s) * theta;
            error(4) = (rot_error(0,2) - rot_error(2,0)) / (2*s) * theta;
            error(5) = (rot_error(1,0) - rot_error(0,1)) / (2*s) * theta;
        } else {
            error.tail(3) = Eigen::Vector3f::Zero();  // 姿态误差为0
        } */

           // 转换为欧拉角，顺序为Z-Y-X
        Eigen::Vector3f euler = rot_error.eulerAngles(2, 1, 0); // Z, Y, X顺序
        error.tail(3) << euler(0), euler(1), euler(2);  // 使用欧拉角表示姿态误差
        
        return error;
    }

    /**
     * @brief 角度归一化到[-PI, PI]
     * @param q 关节角度向量
     */
    void normalizeAngles(Vector7& q) {
        for (int i = 0; i < 7; ++i) {
            while (q(i) > PI) q(i) -= 2*PI;
            while (q(i) < -PI) q(i) += 2*PI;
        }
    }

    /**
     * @brief 逆运动学求解
     * @param q_init 初始关节角度（迭代起点）
     * @param target 目标末端位姿
     * @param q_sol 输出的关节角度解
     * @return 是否求解成功（0=成功，1=未收敛）
     */
    int inverseKinematics(const Vector7& q_init, const Matrix4& target, Vector7& q_sol) {
        Vector7 q_current = q_init;  // 当前关节角度
        int iter = 0;                // 迭代计数器
        
        // 迭代求解
        while (iter < MAX_ITER) {
            // 1. 计算当前末端位姿
            Matrix4 current_pose = forwardKinematics(q_current);
            
            // 2. 计算误差
            Vector6 error = calculateError(current_pose, target);
            
            // 3. 检查是否收敛
            if (error.norm() < EPS) {
                q_sol = q_current;
                normalizeAngles(q_sol);
                return 0;  // 成功收敛
            }
            
            // 4. 计算雅克比矩阵
            Jacobian J = computeJacobian(q_current);
            
            // 5. 计算雅克比伪逆（带阻尼最小二乘）
            Eigen::MatrixXf J_pinv = J.transpose() * (J * J.transpose() + 
                                    DAMPING_FACTOR * Matrix6f::Identity()).inverse();
            
            // 6. 更新关节角度
            q_current += J_pinv * error;
            
            iter++;
        }
        
        // 超过最大迭代次数，返回当前解
        q_sol = q_current;
        normalizeAngles(q_sol);
        return 1;  // 未收敛
    }
};

int main() {
    // 创建逆运动学求解器实例
    IKSolver solver;
    
    // 1. 设置初始关节角度（可根据实际情况调整）
    Vector7 q_init = Vector7::Zero();  // 初始化为零位
    
    // 2. 设置目标位姿（示例：末端在特定位置和姿态）
    Matrix4 target_pose = Matrix4::Identity();
    
    // 目标位置（x, y, z）
    target_pose(0,3) = 0.3;  // x坐标
    target_pose(1,3) = 0.1;  // y坐标
    target_pose(2,3) = 0.5;  // z坐标
    
    // 目标姿态（旋转矩阵，此处为单位矩阵表示水平向前）
    target_pose.block<3,3>(0,0) = Eigen::Matrix3f::Identity();

    // 3. 求解逆运动学
    Vector7 q_sol;
    int result = solver.inverseKinematics(q_init, target_pose, q_sol);
    
    // 4. 输出结果
    if (result == 0) {
        std::cout << "逆运动学求解成功（迭代次数：" << MAX_ITER << "）" << std::endl;
    } else {
        std::cout << "逆运动学未收敛（已达最大迭代次数）" << std::endl;
    }
    
    std::cout << "关节角度解（弧度）：" << std::endl;
    for (int i = 0; i < 7; ++i) {
        std::cout << "关节 " << i+1 << ": " << q_sol(i) << " (" 
                  << q_sol(i)*180/PI << "°)" << std::endl;
    }
    
    // 验证解的正确性（计算正运动学检查误差）
    Matrix4 verify_pose = solver.forwardKinematics(q_sol);
    std::cout << "验证位姿：" << verify_pose << std::endl;
    Vector6 final_error = solver.calculateError(verify_pose, target_pose);
    std::cout << "最终位姿误差 norm：" << final_error.norm() << std::endl;
    
    return 0;
}
    