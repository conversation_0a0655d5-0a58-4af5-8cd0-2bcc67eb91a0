#include <iostream>
#include <math.h>
#include <stdio.h>
#include <string.h>
#include <kdl/kdl.hpp> 
#include <kdl/chain.hpp> 
#include <kdl/tree.hpp> 
#include <kdl/segment.hpp> 
#include <kdl/chainfksolver.hpp> 
#include <kdl/chainfksolverpos_recursive.hpp> 
#include <kdl/frames_io.hpp> 
#include <Eigen/Dense>

using namespace std;
using namespace KDL;
using namespace Eigen;

#define PI 3.14159265358979323846
#define JNT_NUM_L 8
typedef Eigen::Matrix4f Matrix4;          // 4x4变换矩阵

float dh_param_l[8][4] ={{0,0,0, -PI/2},
                          {-PI/2,0.1295f,0, -PI/2},
                          {0,0,-0.357f,-PI/2},
                          {0,0,-0.06f,0},
                          {0,0,-0.2525f,0},
                          {0,0,0, PI/2},
                          {-PI/2,0,0, PI/2},
                          {0,0.1995,0,0}};

float dh_param_r[8][4] ={{0,0,0, -PI/2},
                    {-PI/2,-0.1295f,0, PI/2},
                    {0,0,-0.357f,PI/2},
                    {0,0,-0.06f,0},
                    {0,0,-0.2525f,0},
                    {0,0,0, PI/2},
                    {-PI/2,0,0, PI/2},
                    {0,0.1995,0,0}};


/*************************************************************/
/*  矩阵相乘计算函数: C=AB，C为MxN矩阵，A为MxP，B为PxN
    输入：A[m][p]，B[p][n]
    输出：C[m][n].
*/
/*************************************************************/
void Rbt_MulMtrx(int m, int n, int p, float* A, float* B, float* C)
{
    int i, j, k;
    for (i = 0; i < m; i++)
    {
        for (j = 0; j < n; j++)
        {
            C[n * i + j] = 0.0;
            for (k = 0; k < p; k++)
            {
                C[n * i + j] = C[n * i + j] + A[p * i + k] * B[n * k + j];
            }
        }
    }
}
/*************************************************************/
/*	函数名称:	nfCross
    函数功能:	计算矢量u叉乘v
    参    数:u[3]:输入变量，矢量u；
            v[3]:输入变量，矢量v；
            n[3]:输出变量，u叉乘v后的矢量；
    返 回 值:无
*/
/*************************************************************/
void nfCross(float u[], float v[], float n[])
{
    n[0] = u[1] * v[2] - u[2] * v[1];
    n[1] = u[2] * v[0] - u[0] * v[2];
    n[2] = u[0] * v[1] - u[1] * v[0];
}

/*************************************************************/
/*矩阵求逆计算函数*/
/*************************************************************/
int Rbt_InvMtrx(float* C, float* IC, int n)
{
    int i, j, k, l;
    /* 单位阵*/
    for (i = 0; i < n; i++)
    {
        for (j = 0; j < n; j++)
        {
            *(IC + i * n + j) = 0.0;
        }
        *(IC + i * n + i) = 1.0;
    }
    /* 化上三角阵*/
    for (j = 0; j < n; j++)
    {
        if (fabs(*(C + j * n + j)) > 1e-15) /* C[j][j]不等于0*/
        {
            /* IC阵的第j行除以C[j][j]*/
            for (k = 0; k < n; k++)
            {
                *(IC + j * n + k) /= *(C + j * n + j);
            }
            /* C阵的第j行除以C[j][j]*/
            for (k = n - 1; k >= j; k--)
            {
                *(C + j * n + k) /= *(C + j * n + j);
            }

            for (i = j + 1; i < n; i++)
            {
                /* IC阵的第i行 - C[i][j]*IC阵的第j行*/
                for (k = 0; k < n; k++)
                {
                    *(IC + i * n + k) -= *(C + i * n + j) * *(IC + j * n + k);
                }
                /* C阵的第i行 - C[i][j]*C阵的第j行*/
                for (k = n - 1; k >= j; k--)
                {
                    *(C + i * n + k) -= *(C + i * n + j) * *(C + j * n + k);
                }
            }
        }
        else if (j < n - 1)
        {

            for (l = j + 1; l < n; l++)
            {
                /* 若C阵第j行后的C[l][j]不等于0，第j行加上第l行*/
                if (fabs(*(C + l * n + j)) > 1e-15)
                {
                    for (k = 0; k < n; k++)
                    {
                        *(IC + j * n + k) += *(IC + l * n + k);
                    }
                    for (k = n - 1; k >= j; k--)
                    {
                        *(C + j * n + k) += *(C + l * n + k);
                    }
                    /* IC阵的第j行除以C[j][j]*/
                    for (k = 0; k < n; k++)
                    {
                        *(IC + j * n + k) /= *(C + j * n + j);
                    }
                    /* C阵的第j行除以C[j][j]*/
                    for (k = n - 1; k >= j; k--)
                    {
                        *(C + j * n + k) /= *(C + j * n + j);
                    }
                    /* 第i行 - C[i][j]*第j行*/
                    for (i = j + 1; i < n; i++)
                    {
                        for (k = 0; k < n; k++)
                        {
                            *(IC + i * n + k) -= *(C + i * n + j) * *(IC + j * n + k);
                        }
                        for (k = n - 1; k >= j; k--)
                        {
                            *(C + i * n + k) -= *(C + i * n + j) * *(C + j * n + k);
                        }
                    }
                    break;
                }
            }

            if (l == n)  /* C[l][j] 全等于0*/
            {
                return (-1);   /*  矩阵的行列式为零，不可求逆*/
            }
        }
        else  /* C[n][n]等于0*/
        {
            return (-1);    /*  矩阵的行列式为零，不可求逆*/
        }
    }
    /* 化成单位阵*/
    for (j = n - 1; j >= 1; j--)
    {
        for (i = j - 1; i >= 0; i--)
        {
            for (k = 0; k < n; k++)
            {
                *(IC + i * n + k) -= *(C + i * n + j) * *(IC + j * n + k);
            }
            *(C + i * n + j) = 0;
        }
    }

    return (1);
}
/************************************************************************/
/*		根据DH参数，计算机械臂的Jacobian矩阵，在基坐标系中的表示
        DH=[theta, alpha, a, d]
        输入：float DH_OBC[][4], DH参数表
              floatJointAngle10[],当前绳驱空间关节角
        输出：float dRbtJcb[][JNT_NUM_L],速度雅可比矩阵
              float T0n_c[][4]，当前末端姿态矩阵
*/
/************************************************************************/

void Rbt_CalJcb(float DH_OBC[][4], float JointAngle8[], float dRbtJcb[][8], float T0n_c[][4])
{
    float T1[4][4], T2[4][4], Tn[4][4], alpha_sr[JNT_NUM_L], a_sr[JNT_NUM_L], d_sr[JNT_NUM_L], qq[JNT_NUM_L], q0_sr[JNT_NUM_L];
    float zi_1[3], zi_1_All[3][JNT_NUM_L], P_i_1_n[3], P_i_All[3][JNT_NUM_L], Cros_Z_P[3];
    int i, j, k;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        q0_sr[i] = DH_OBC[i][0];
        alpha_sr[i] = DH_OBC[i][3];
        a_sr[i] = DH_OBC[i][2];
        d_sr[i] = DH_OBC[i][1];
    }

    Tn[0][0] = 1;    Tn[0][1] = 0;   Tn[0][2] = 0;  Tn[0][3] = 0;
    Tn[1][0] = 0;    Tn[1][1] = 1;   Tn[1][2] = 0;  Tn[1][3] = 0;
    Tn[2][0] = 0;    Tn[2][1] = 0;   Tn[2][2] = 1;  Tn[2][3] = 0;
    Tn[3][0] = 0;    Tn[3][1] = 0;   Tn[3][2] = 0;  Tn[3][3] = 1;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        for (j = 0;j < 3;j++)
        {
            zi_1_All[j][i] = Tn[j][2];			//Tn[j][2]为z向量
        }

        qq[i] = q0_sr[i] + JointAngle8[i];
        T1[0][0] = (float)(cos(qq[i]));                    T1[0][1] = -(float)(cos(alpha_sr[i]) * sin(qq[i]));
        T1[0][2] = (float)(sin(alpha_sr[i]) * sin(qq[i]));   T1[0][3] = (float)(a_sr[i] * cos(qq[i]));

        T1[1][0] = (float)(sin(qq[i]));                    T1[1][1] = (float)(cos(alpha_sr[i]) * cos(qq[i]));
        T1[1][2] = -(float)(sin(alpha_sr[i]) * cos(qq[i]));   T1[1][3] = (float)(a_sr[i] * sin(qq[i]));
        T1[2][0] = 0;  T1[2][1] = (float)(sin(alpha_sr[i])); T1[2][2] = (float)(cos(alpha_sr[i]));   T1[2][3] = d_sr[i];
        T1[3][0] = 0;  T1[3][1] = 0; T1[3][2] = 0;   T1[3][3] = 1;
        Rbt_MulMtrx(4, 4, 4, Tn[0], T1[0], T2[0]);
        for (j = 0;j < 4;j++)
        {
            for (k = 0;k < 4;k++)
            {
                Tn[j][k] = T2[j][k];				//正运动学
                /*	Trans_Matrix[j][4*i+k] = T6[j][k];*/
            }
        }
        for (j = 0;j < 3;j++) P_i_All[j][i] = Tn[j][3]; //Tn[j][3]为p向量
    }
    for (j = 0;j < 3;j++)
    {
        P_i_1_n[j] = P_i_All[j][JNT_NUM_L - 1];  /*P_0_n*/
    }
    for (i = 0; i < JNT_NUM_L; i++)
    {
        for (j = 0;j < 3;j++)
        {
            zi_1[j] = zi_1_All[j][i];
        }
        nfCross(zi_1, P_i_1_n, Cros_Z_P); //矢量积
        for (j = 0;j < 3;j++)
        {
            dRbtJcb[j][i] = Cros_Z_P[j];
            dRbtJcb[j + 3][i] = zi_1[j];
            P_i_1_n[j] = P_i_All[j][JNT_NUM_L - 1] - P_i_All[j][i];  /*P_0_n*/
        }
    }
    for (i = 0;i < 4;i++)
    {
        for (j = 0;j < 4;j++)
        {
            T0n_c[i][j] = Tn[i][j];   /*---返回当前的末端位姿矩阵--*/
        }
    }
}

/*************************************************************/
/*---计算广义逆矩阵， 6X7， m《=n---  Moore-penrose广义逆
  ----A_pinv = A'*(A*A')^(-1)---
  */
/*************************************************************/
void Rbt_PInvMtrx67(float AA[][7], float AA_pinv[][6])
{
    int i, j;
    float AA_T[7][6], BB[6][6], BB_Inv[6][6];

    for (i = 0; i < 7; i++)
    {
        for (j = 0; j < 6; j++)
        {
            AA_T[i][j] = AA[j][i];
        }
    }

    Rbt_MulMtrx(6, 6, 7, AA[0], AA_T[0], BB[0]); /*---BB = AA*AA_T----*/

    for (j = 0; j < 6; j++)
    {
        BB[j][j] = BB[j][j]+0.001;
    }

    Rbt_InvMtrx(BB[0], BB_Inv[0], 6);
    Rbt_MulMtrx(7, 6, 6, AA_T[0], BB_Inv[0], AA_pinv[0]); /*---BB = AA*AA_T----*/
}

/************************************************************************/
/*-----	数值解求解机械臂逆运动学
    功能：根据期望末端矩阵和当前关节角求解期望关节角
    输入：float DH_OBC[][4]DH参数
          float T0n[][4]期望末端位姿矩阵
          float JntCurrent10[]当前关节角
    输出：float JntCalcted10[]计算期望关节角
*/
/************************************************************************/
int nfInvKinePosLev(float DH_OBC[][4], float T0n[][4], float JntCurrent8[], float JntCalcted8[])
{
    float DH_q0[JNT_NUM_L], DH_alpha[JNT_NUM_L], DH_a[JNT_NUM_L], DH_d[JNT_NUM_L];
    float T0n_c[4][4], dA[6], AttErrVec[3];
    float Jcb_0[6][JNT_NUM_L], Jcb_0_pinv[JNT_NUM_L][6], Jcb_0_simple[6][7];
    float q_Itera8[JNT_NUM_L], dq[7], dp_norm, do_norm;  /*---用于迭代的中间变量---*/

    float efs,efs_o;
    int solutionFlag;
    float n[3], o[3], a[3], nd[3], od[3], ad[3];
    float Cros1[3], Cros2[3], Cros3[3];


    float u[JNT_NUM_L][7] =
        {
            {1.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		1.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		1.0f,		0.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		1.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		1.0f,		0.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		0.0f,		1.0f,		0.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		1.0f,		0.0f},
            {0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		0.0f,		1.0f}     /*简化雅可比的系数矩阵*/
        };

    int i, lmax, l_limit;
    lmax = 0;
    l_limit = 15000;
    dp_norm = 1;
    do_norm = 1;
    efs = (float)(1e-6);//1.0e-6
    efs_o = (float)(1e-6);//1.0e-6
    solutionFlag = 0;
    float d_Jnt[10]={};
    for (i = 0; i < JNT_NUM_L; i++)
    {
        DH_q0[i] = DH_OBC[i][0];
        DH_alpha[i] = DH_OBC[i][3];
        DH_a[i] = DH_OBC[i][2];
        DH_d[i] = DH_OBC[i][1];

        q_Itera8[i] = JntCurrent8[i];
    }

    for (i = 0; i < 3; i++)
    {

        nd[i] = T0n[i][0];
        od[i] = T0n[i][1];
        ad[i] = T0n[i][2];
    }

    while (lmax <= l_limit)
    {
        //        if ((dp_norm > efs) || (do_norm > efs_o)||(d_Jnt[0]>0.1045329f)||(d_Jnt[2]>0.2045329f)||(d_Jnt[3]>0.4045329f)||(d_Jnt[4]>0.1045329f)||(d_Jnt[5]>0.5045329f)||(d_Jnt[6]>0.1045329f)) //这里限制了关节单次不得超过10度
        if ((dp_norm > efs) || (do_norm > efs_o)) //这里限制了关节单次不得超过10度
        {
            solutionFlag = 0;

            // Rbt_CalJcb(DH_OBC, q_Itera11, Jcb_0, T0n_c);
            Rbt_CalJcb(DH_OBC, q_Itera8, Jcb_0, T0n_c);

            for (i = 0; i < 3; i++)
            {
                n[i] = T0n_c[i][0];
                o[i] = T0n_c[i][1];
                a[i] = T0n_c[i][2];
            }

            nfCross(n, nd, Cros1);
            nfCross(o, od, Cros2);
            nfCross(a, ad, Cros3);

            for (i = 0; i < 3; i++)
            {
                AttErrVec[i] = 0.5f * (Cros1[i] + Cros2[i] + Cros3[i]); //---姿态误差
                dA[i] = T0n[i][3] - T0n_c[i][3];                        //位置误差
                dA[i + 3] = AttErrVec[i];                               //姿态误差
            }

            Rbt_MulMtrx(6, 7, JNT_NUM_L, Jcb_0[0], u[0], Jcb_0_simple[0]);

            Rbt_PInvMtrx67(Jcb_0_simple, Jcb_0_pinv);                   //Moore-penrose广义逆

            Rbt_MulMtrx(7, 1, 6, Jcb_0_pinv[0], dA, dq);                // ---ARM_b用于平衡质心位置和姿态

            dp_norm = 0;
            do_norm = 0;
            for (i = 0; i < 3; i++)
            {
                dp_norm = dp_norm + dA[i] * dA[i];
                do_norm = do_norm + dA[i + 3] * dA[i + 3];//误差的模的平方
            }

            q_Itera8[0] = q_Itera8[0] + dq[0];
            q_Itera8[1] = q_Itera8[1] + dq[1];
            q_Itera8[2] = q_Itera8[2] + dq[2];
            q_Itera8[3] = q_Itera8[3] + dq[3];
            q_Itera8[4] = q_Itera8[4] + dq[3];
            q_Itera8[5] = q_Itera8[5] + dq[4];
            q_Itera8[6] = q_Itera8[6] + dq[5];
            q_Itera8[7] = q_Itera8[7] + dq[6];

            /************************************************************************/
            /*  关节角范围    还需要进一步确认                                                 */
            /************************************************************************/

            float q_limit_low_l[] = {-45*3.1415926f/180,-90*3.1415926f/180,0*3.1415926f/180,-20*3.1415926f/180,-20*3.1415926f/180,-80*3.1415926f/180,-80*3.1415926f/180,-90*3.1415926f/180};
            float q_limit_high_l[] = {45*3.1415926f/180,90*3.1415926f/180,90*3.1415926f/180,80*3.1415926f/180,80*3.1415926f/180,80*3.1415926f/180,80*3.1415926f/180,90*3.1415926f/180};

            for (int i = 0;i<JNT_NUM_L;i++) {

                //                cout<<"i="<<lmax<<" ; "<<q_Itera10[i]<<endl;

                if (q_Itera8[i]<q_limit_low_l[i])
                {
                    q_Itera8[i]=q_limit_low_l[i];

                }
                else if (q_Itera8[i]>q_limit_high_l[i])
                {

                    q_Itera8[i]=q_limit_high_l[i];

                }
            }

            lmax = lmax + 1;  /*---更新迭代次数---*/

        }
        else
        {
            solutionFlag = 1;   /*---求解成功---*/

            lmax = l_limit + 111;  /*---求解成功，直接退出---*/
        }

    }

    if (solutionFlag == 1)
    {
        /************************************************************************/
        /*  限制到范围[-pi, pi]---                                                    */
        /************************************************************************/
        for (i = 0;i < JNT_NUM_L;i++)
        {
            while (q_Itera8[i] > 3.14171592654) q_Itera8[i] = q_Itera8[i] - 6.283185307f;
            while (q_Itera8[i] < -3.14171592654) q_Itera8[i] = q_Itera8[i] + 6.283185307f;
        }
        for (i = 0; i < JNT_NUM_L; i++)
        {
            JntCalcted8[i] = q_Itera8[i];
        }
    }
    else
    {
        for (i = 0; i < JNT_NUM_L; i++)
        {
            // JntCalcted11[i] = JntCurrent11[i];
            JntCalcted8[i] = JntCurrent8[i];
        }
    }
    return solutionFlag;
}

/*************************************************************/
/*	函数名称:	nfFkineSpaceRbt
    函数功能:	（关节空间位置级正运动学）根据机械臂关节角计算空间机器人末端工具坐标相对于机械臂安装坐标系的位姿
    参   数: JntCurrent10[]:输入变量，机械臂关节角，弧度
             T0n:输出变量，机械臂末端相对于机械臂安装坐标系的齐次矩阵
    返回值:无
*/
/*************************************************************/
void nfFkineSpaceRbt(float JntCurrent8[], float DH_OBC[][4], float T0n[][4])
{

    float T1[4][4], T2[4][4], alpha_sr[JNT_NUM_L], a_sr[JNT_NUM_L], d_sr[JNT_NUM_L], qq[JNT_NUM_L], q0_sr[JNT_NUM_L];//, Tr0[4][4];
    int i, j, k;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        q0_sr[i] = DH_OBC[i][0];
        alpha_sr[i] = DH_OBC[i][3];
        a_sr[i] = DH_OBC[i][2];
        d_sr[i] = DH_OBC[i][1];
    }

    T0n[0][0] = 1;    T0n[0][1] = 0;   T0n[0][2] = 0;  T0n[0][3] = 0;
    T0n[1][0] = 0;    T0n[1][1] = 1;   T0n[1][2] = 0;  T0n[1][3] = 0;
    T0n[2][0] = 0;    T0n[2][1] = 0;   T0n[2][2] = 1;  T0n[2][3] = 0;
    T0n[3][0] = 0;    T0n[3][1] = 0;   T0n[3][2] = 0;  T0n[3][3] = 1;

    for (i = 0; i < JNT_NUM_L; i++)
    {
        qq[i] = q0_sr[i] + JntCurrent8[i];
        T1[0][0] = (float)cos(qq[i]);                    T1[0][1] = -(float)(cos(alpha_sr[i]) * sin(qq[i]));
        T1[0][2] = (float)(sin(alpha_sr[i]) * sin(qq[i]));   T1[0][3] = (float)(a_sr[i] * cos(qq[i]));

        T1[1][0] = (float)(sin(qq[i]));                    T1[1][1] = (float)(cos(alpha_sr[i]) * cos(qq[i]));
        T1[1][2] = -(float)(sin(alpha_sr[i]) * cos(qq[i]));   T1[1][3] = (float)(a_sr[i] * sin(qq[i]));

        T1[2][0] = 0;  T1[2][1] = (float)(sin(alpha_sr[i])); T1[2][2] = (float)(cos(alpha_sr[i]));   T1[2][3] = d_sr[i];

        T1[3][0] = 0;  T1[3][1] = 0; T1[3][2] = 0;   T1[3][3] = 1;

        Rbt_MulMtrx(4, 4, 4, T0n[0], T1[0], T2[0]);

        for (j = 0;j < 4;j++)
        {
            for (k = 0;k < 4;k++)
            {
                T0n[j][k] = T2[j][k];
            }
        }
    }
}

    /**
     * @brief 计算单个关节的DH变换矩阵
     * @param q 关节角度（弧度）
     * @param alpha 扭角（弧度）
     * @param a 连杆长度
     * @param d 连杆偏距
     * @return 4x4变换矩阵
     */
    Matrix4 dhTransform(float q, float alpha, float a, float d) {
        Matrix4 T;
        T << cos(q), -cos(alpha)*sin(q),  sin(alpha)*sin(q), a*cos(q),
             sin(q),  cos(alpha)*cos(q), -sin(alpha)*cos(q), a*sin(q),
             0,       sin(alpha),         cos(alpha),         d,
             0,       0,                  0,                  1;
        return T;
    }

    /**
     * @brief 正运动学计算：根据关节角度计算末端位姿
     * @param q 关节角度向量
     * @return 末端相对于基坐标系的变换矩阵
     */
    Matrix4 forwardKinematics(const VectorXf q,MatrixXf DH) {
        Matrix4 T_total = Matrix4::Identity();  // 初始化为单位矩阵
        
        // 遍历所有关节，累积变换矩阵
        for (int i = 0; i < 8; ++i) {
            // 从DH参数表获取参数，q = 输入关节角 + 偏移量
            float q_i = q(i) + DH(i, 0);  
            float alpha_i = DH(i, 3);
            float a_i = DH(i, 2);
            float d_i = DH(i, 1);
            
            // 计算当前关节的变换矩阵并累积
            T_total *= dhTransform(q_i, alpha_i, a_i, d_i);
        }
        return T_total;
    }

int main()
{
    // MatrixXf DH(8,4);
    // DH << 0,0,0, -PI/2,
    //     -PI/2,0.1295f,0, -PI/2,
    //     0,0,-0.357f,-PI/2,
    //     0,0,-0.06f,0,
    //     0,0,-0.2525f,0,
    //     0,0,0, PI/2,
    //     0,0,0, PI/2,
    //     0,0.1995,0,0;

    Eigen::MatrixXf DH = Eigen::Map<Eigen::Matrix<float, Eigen::Dynamic, Eigen::Dynamic, Eigen::RowMajor>>(
        &dh_param_l[0][0],  // 数组首地址
        8,        // 行数
        4         // 列数
    );
    
    float dh_param[8][4] = {0};
    memcpy(dh_param, dh_param_l, sizeof(dh_param_l));
    float q_init[8] = {0};
    float JntCalcted8[8] = {0};
    float Target[4][4] = {0};
    Rotation R = Rotation::RotX(PI/2) * Rotation::RotY(PI/2);
    for(size_t i = 0; i < 3; i++)
    {
        for(size_t j = 0; j < 3; j++)
        {
            Target[i][j] = R(i,j);
        }
    }
    Target[0][3] = 0.3;
    Target[1][3] = 0.35;
    Target[2][3] = -0.35;
    nfInvKinePosLev(dh_param, Target, q_init, JntCalcted8);
    for(size_t i = 0; i < 8; i++)
    {
        cout << JntCalcted8[i] << endl;
    }

    float T_cal[4][4] = {0};
    nfFkineSpaceRbt(JntCalcted8,dh_param,T_cal);
    Eigen::VectorXf q = Eigen::Map<Eigen::VectorXf>(JntCalcted8, 8);

    Matrix4 T = forwardKinematics(q,DH);
    cout<<"T_cal:----------------------------"<<endl;
    for(int i = 0;i<4;i++)
    {
        for(int j=0;j<4;j++)
        {
            cout<< T_cal[i][j]<<"  ";
        }
        cout<<endl;
    }

    cout<< T <<endl;

    return 0;
}