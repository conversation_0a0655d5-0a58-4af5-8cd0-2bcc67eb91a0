#include "plan.h"
#include <std_msgs/UInt8MultiArray.h>

bool grasp_finshed_r = false;
std::vector<double> container;
bool detect_flag = false;
vector<double>finger_read(15);
vector<double>finger_write(15);

bool finger_arrvied(vector<double> finger_read,vector<double>finger_write)
{
    for(size_t i = 0; i < 15; i++)
    {
        if(abs(finger_read[i] - finger_write[i]) > 0.1)
        {
            return false;
        }
    }
    return true;
}

void grasp_callback_right(const std_msgs::UInt8MultiArray::ConstPtr &msg)
{
    for(size_t i = 0; i < 15; i++)
    {
        finger_read[i] = msg->data[i];
    }
    grasp_finshed_r = finger_arrvied(finger_read,finger_write);
    return;
}

void camera_array_callback(const std_msgs::Float64MultiArray::ConstPtr& msg)
{
    //  ROS_INFO("Received array: ");
     detect_flag = true;
    
     for (double value : msg->data) {
            container.push_back(value);
            // cout << "orange: " << value << endl;
    }
}
int main(int argc, char **argv)
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "grasp_demo");
    ros::NodeHandle nh;
    ros::Subscriber vision_sub = nh.subscribe("/gripper_det_box", 1, camera_array_callback);
    ros::Subscriber finger_sub = nh.subscribe("/read_angle", 1, grasp_callback_right);
    ros::Publisher finger_pub = nh.advertise<std_msgs::UInt8MultiArray>("/set_angle", 1);
    std_msgs::UInt8MultiArray finger_data;
    finger_data.data.resize(15);
    finger_write = {0, -0, 300, 0, 0, 0, 0, 0, 0, 0,0, 0, 0, 0, 0};
    vector<int>fd = angleToMotor(finger_write);
    for(size_t i = 0; i < 15; i++)
    {
        finger_data.data[i] = fd[i];
    }
    ros::AsyncSpinner spinner(3);
    spinner.start();
    ros::Rate loop_rate(1000);
    const char* arm = "right";
    robot_control robot(arm);
    float q_cur[8] = {0};
    float JntCalcted8[8] = {0};
    float Target[4][4] = {0};
    float tf = 8;
    int sucess_exit = 0;
    Vector8f q_current = Vector8f::Zero();
    // Matrix4 T = robot.forwardKinematics(q_current);
    // cout << "当前变换矩阵：" << T << endl;
    Rotation R = Rotation::RotX(PI/2) * Rotation::RotY(PI/2);
    for(size_t i = 0; i < 3; i++)
    {
        for(size_t j = 0; j < 3; j++)
        {
            Target[i][j] = R(i,j);
        }
    }
    Target[0][3] = 0.5;
    Target[1][3] = -0.1295;
    Target[2][3] = -0.3;
    sucess_exit = robot.nfInvKinePosLev(Target, q_cur, JntCalcted8);
    if(sucess_exit == 0)
    {
        ROS_ERROR("IK failed");
        return 0;
    }
    for(size_t i = 0; i < 8; i++)
    {
        cout << JntCalcted8[i] << endl;
    }
    Vector14f q_init = Vector14f::Zero();
    Vector14f q_end = Vector14f::Zero();
    // 右臂中间值
    q_end(8) = -1.4;
    q_end(10) = 1.1;
    //左臂中间数值
    // q_end(1) = -1.4;
    // q_end(3) = 1.1;
    robot.pub2Rviz(nh,tf,q_init,q_end);//仿真中共14个关节
    q_init = q_end;
    Vector7f q_goal = Vector7f::Zero();
    robot.Jnt8to7(JntCalcted8, q_goal);
    for(size_t i = 0; i < 7; i++)
    {
        // q_end(i) = q_goal(i);
        q_end(i+7) = q_goal(i);//右臂

    }
    /* 第一关节 */
    q_end(0) = -q_end(0);
    // q_end(7) = -q_end(7);
    /* 第二关节 */
    q_end(8) = -q_end(8);
    q_end(1) = -q_end(1);
    //右6
    q_end(12) = -q_end(12);

    robot.pub2Rviz(nh,tf,q_init,q_end);
    /* 确保抓住 */
    /*     while(ros::ok() && !grasp_finshed_r)
    {
        finger_pub.publish(finger_data);
        ros::spinOnce();
        loop_rate.sleep();
        ROS_INFO("grasp_finshed_r = %d",grasp_finshed_r);
    } */
    /* 移动到水杯上方,递出去 */
    cout << "第二段运动！" << endl;
    q_init = q_end;
    // Target[0][3] += 0.1;
    // Target[1][3] += 0.1;
    // Target[2][3] += 0.15;
    Target[0][3] = 0.25;
    Target[1][3] = -0.0;
    Target[2][3] = -0.15;
    R = Rotation::RotX(PI/2) * Rotation::RotY(PI);
    for(size_t i = 0; i < 3; i++)
    {
        for(size_t j = 0; j < 3; j++)
        {
            Target[i][j] = R(i,j);
        }
    }
   /* 依据当前角度求逆解 */
    // for(size_t i = 0;i < 8; i++)
    // {
    //     q_cur[i] = JntCalcted8[i];

    // }
    sucess_exit = robot.nfInvKinePosLev(Target, q_cur, JntCalcted8);
    if(sucess_exit == 0)
    {
        ROS_ERROR("IK failed");
        return 0;
    }
    for(size_t i = 0;i < 8; i++)
    {
        q_current[i] = JntCalcted8[i];
        cout << JntCalcted8[i] << endl;

    }
    Matrix4 T = robot.forwardKinematics(q_current);
    cout << "变换后变换矩阵：" << T << endl;
    robot.Jnt8to7(JntCalcted8, q_goal);
    for(size_t i = 0; i < 7; i++)
    {
        // q_end(i) = q_goal(i);
        q_end(i+7) = q_goal(i);//右臂

    }
    q_end(0) = -q_end(0);
    q_end(8) = -q_end(8);
    q_end(1) = -q_end(1);
    q_end(12) = -q_end(12);

    robot.pub2Rviz(nh,tf,q_init,q_end);

    //倾倒
    // q_init = q_end;
    // q_end(13) += PI/3;

    // robot.pub2Rviz(nh,tf,q_init,q_end);

    return 0;
}