#include <iostream>
#include <vector>
#include <cmath>
#include <random>
#include <Eigen/Dense>
#include <algorithm>
#include <memory>

// 定义关节空间维度（7自由度机械臂）
const int DOF = 7;
// 关节角度范围（单位：弧度）
const double JOINT_LIMITS[DOF][2] = {
    {-M_PI/2, M_PI/2},   // 关节1范围
    {-M_PI/2, M_PI/2},   // 关节2范围
    {-M_PI/2, M_PI/2},   // 关节3范围
    {-M_PI/2, M_PI/2},   // 关节4范围
    {-M_PI/2, M_PI/2},   // 关节5范围
    {-M_PI/2, M_PI/2},   // 关节6范围
    {-M_PI/2, M_PI/2}    // 关节7范围
};

// 障碍物定义（简单立方体）
struct Obstacle {
    Eigen::Vector3d center;  // 中心点
    double size;             // 边长的一半
};

// RRT节点结构
struct Node {
    Eigen::VectorXd jointAngles;  // 关节角度
    std::shared_ptr<Node> parent; // 父节点
    double cost;                  // 路径成本
    
    Node() : jointAngles(DOF), parent(nullptr), cost(0.0) {}
    Node(const Eigen::VectorXd& angles) : jointAngles(angles), parent(nullptr), cost(0.0) {}
};

// 随机数生成器
std::random_device rd;
std::mt19937 gen(rd());
std::uniform_real_distribution<double> dis(0.0, 1.0);

// 计算两个关节空间点之间的距离
double distance(const Eigen::VectorXd& a, const Eigen::VectorXd& b) {
    return (a - b).norm();
}

// 检查关节角度是否在范围内
bool isWithinJointLimits(const Eigen::VectorXd& angles) {
    for (int i = 0; i < DOF; ++i) {
        if (angles[i] < JOINT_LIMITS[i][0] || angles[i] > JOINT_LIMITS[i][1]) {
            return false;
        }
    }
    return true;
}

// 正运动学：从关节角度计算末端位置
Eigen::Vector3d forwardKinematics(const Eigen::VectorXd& jointAngles) {
    // 简化的正运动学模型，实际应用中需替换为机械臂的真实DH参数
    Eigen::Vector3d endEffector;
    
    // 这里使用简单的模型作为示例
    endEffector[0] = cos(jointAngles[0]) * (0.5 + 0.3 * cos(jointAngles[1]) + 0.2 * cos(jointAngles[2]));
    endEffector[1] = sin(jointAngles[0]) * (0.5 + 0.3 * cos(jointAngles[1]) + 0.2 * cos(jointAngles[2]));
    endEffector[2] = 0.1 + 0.3 * sin(jointAngles[1]) + 0.2 * sin(jointAngles[2]);
    
    // 加入其他关节的影响（简化）
    for (int i = 3; i < DOF; ++i) {
        endEffector[i % 3] += 0.1 * sin(jointAngles[i]);
    }
    
    return endEffector;
}

// 检查机械臂是否与障碍物碰撞
bool isCollision(const Eigen::VectorXd& jointAngles, const Obstacle& obstacle) {
    // 简化的碰撞检测：只检测末端执行器是否与障碍物碰撞
    Eigen::Vector3d endPos = forwardKinematics(jointAngles);
    Eigen::Vector3d diff = endPos - obstacle.center;
    
    // 检查是否在立方体障碍物内
    if (fabs(diff[0]) < obstacle.size && 
        fabs(diff[1]) < obstacle.size && 
        fabs(diff[2]) < obstacle.size) {
        return true;  // 碰撞
    }
    
    // 这里可以添加更多的碰撞检测，如检测各连杆是否与障碍物碰撞
    
    return false;  // 无碰撞
}

// 从树中找到最近的节点
std::shared_ptr<Node> findNearestNode(const std::vector<std::shared_ptr<Node>>& tree, const Eigen::VectorXd& target) {
    if (tree.empty()) return nullptr;
    
    std::shared_ptr<Node> nearest = tree[0];
    double minDist = distance(nearest->jointAngles, target);
    
    for (const auto& node : tree) {
        double dist = distance(node->jointAngles, target);
        if (dist < minDist) {
            minDist = dist;
            nearest = node;
        }
    }
    
    return nearest;
}

// 生成新的节点
std::shared_ptr<Node> steer(const std::shared_ptr<Node>& from, const Eigen::VectorXd& to, double stepSize) {
    Eigen::VectorXd direction = to - from->jointAngles;
    double dist = direction.norm();
    
    if (dist <= stepSize) {
        return std::make_shared<Node>(to);
    } else {
        Eigen::VectorXd newAngles = from->jointAngles + direction.normalized() * stepSize;
        return std::make_shared<Node>(newAngles);
    }
}

// 随机生成一个关节空间点
Eigen::VectorXd randomConfig() {
    Eigen::VectorXd config(DOF);
    for (int i = 0; i < DOF; ++i) {
        config[i] = JOINT_LIMITS[i][0] + dis(gen) * (JOINT_LIMITS[i][1] - JOINT_LIMITS[i][0]);
    }
    return config;
}

// 检查从一个节点到另一个节点的路径是否无碰撞
bool isPathValid(const Eigen::VectorXd& from, const Eigen::VectorXd& to, const Obstacle& obstacle, int steps = 10) {
    Eigen::VectorXd direction = to - from;
    double step = 1.0 / steps;
    
    for (int i = 1; i <= steps; ++i) {
        Eigen::VectorXd intermediate = from + direction * step * i;
        if (!isWithinJointLimits(intermediate) || isCollision(intermediate, obstacle)) {
            return false;
        }
    }
    return true;
}

// RRT算法实现
std::vector<Eigen::VectorXd> rrtPlanner(
    const Eigen::VectorXd& start, 
    const Eigen::VectorXd& goal, 
    const Obstacle& obstacle,
    double goalTolerance = 0.1,
    double stepSize = 0.1,
    int maxIterations = 5000) {
    
    // 初始化树
    std::vector<std::shared_ptr<Node>> tree;
    tree.push_back(std::make_shared<Node>(start));
    
    // 检查起点和终点是否合法
    if (!isWithinJointLimits(start) || isCollision(start, obstacle)) {
        std::cerr << "起点不合法或与障碍物碰撞！" << std::endl;
        return {};
    }
    
    if (!isWithinJointLimits(goal) || isCollision(goal, obstacle)) {
        std::cerr << "终点不合法或与障碍物碰撞！" << std::endl;
        return {};
    }
    
    for (int iter = 0; iter < maxIterations; ++iter) {
        // 随机采样或直接使用目标点（增加找到目标的概率）
        Eigen::VectorXd randomPoint;
        if (dis(gen) < 0.1) {  // 10%的概率直接采样目标点
            randomPoint = goal;
        } else {
            randomPoint = randomConfig();
        }
        
        // 找到最近的节点
        std::shared_ptr<Node> nearest = findNearestNode(tree, randomPoint);
        if (!nearest) continue;
        
        // 生成新节点
        std::shared_ptr<Node> newNode = steer(nearest, randomPoint, stepSize);
        
        // 检查新节点是否有效且路径无碰撞
        if (!isWithinJointLimits(newNode->jointAngles)) continue;
        if (!isPathValid(nearest->jointAngles, newNode->jointAngles, obstacle)) continue;
        
        // 添加新节点到树
        newNode->parent = nearest;
        newNode->cost = nearest->cost + distance(nearest->jointAngles, newNode->jointAngles);
        tree.push_back(newNode);
        
        // 检查是否到达目标附近
        if (distance(newNode->jointAngles, goal) < goalTolerance) {
            std::cout << "找到路径！迭代次数: " << iter << std::endl;
            
            // 回溯构建路径
            std::vector<Eigen::VectorXd> path;
            std::shared_ptr<Node> current = newNode;
            
            while (current) {
                path.push_back(current->jointAngles);
                current = current->parent;
            }
            
            // 反转路径，从起点到终点
            std::reverse(path.begin(), path.end());
            
            // 添加终点
            path.push_back(goal);
            
            return path;
        }
    }
    
    std::cerr << "达到最大迭代次数，未找到路径！" << std::endl;
    return {};
}

int main() {
    // 初始化起点和终点的关节角度
    Eigen::VectorXd start(DOF);
    Eigen::VectorXd goal(DOF);
    
    // 设置起点（例如，初始位置）
    start << 0, 0, 0, 0, 0, 0, 0;
    
    // 设置终点（例如，目标位置）
    goal << M_PI/3, M_PI/4, -M_PI/6, M_PI/4, -M_PI/3, M_PI/6, 0;
    
    // 定义障碍物（位于工作空间中央）
    Obstacle obstacle;
    obstacle.center << 0.3, 0.3, 0.3;  // 障碍物中心位置
    obstacle.size = 0.15;              // 障碍物大小（半边长）
    
    // 运行RRT规划
    std::vector<Eigen::VectorXd> path = rrtPlanner(start, goal, obstacle);
    
    // 输出路径
    if (!path.empty()) {
        std::cout << "规划路径包含 " << path.size() << " 个点：" << std::endl;
        for (size_t i = 0; i < path.size(); ++i) {
            std::cout << "点 " << i << ": ";
            for (int j = 0; j < DOF; ++j) {
                std::cout << path[i][j] * 180 / M_PI << "° ";  // 转换为角度显示
            }
            std::cout << std::endl;
            
            // 输出末端位置，验证是否避开障碍物
            Eigen::Vector3d pos = forwardKinematics(path[i]);
            std::cout << "  末端位置: (" << pos[0] << ", " << pos[1] << ", " << pos[2] << ")" << std::endl;
        }
    }
    
    return 0;
}
