#include <robotiq_2f_gripper_msgs/CommandRobotiqGripperActionGoal.h>
#include <ros/ros.h>


int main(int argc,char** argv)
{
    ros::init(argc, argv, "dual_gripper_bridge_node");
    ros::NodeHandle nh_;
    ros::Publisher right_pub_ = nh_.advertise<robotiq_2f_gripper_msgs::CommandRobotiqGripperActionGoal>(
            "/right_gripper/command_robotiq_action/goal", 1);
    ros::Publisher left_pub_ = nh_.advertise<robotiq_2f_gripper_msgs::CommandRobotiqGripperActionGoal>(
            "/left_gripper/command_robotiq_action/goal", 1);
    ros::Rate loop_rate(1000);
    double position_m = 0.15;
    // 动态速度 (开得越大越快)
    double speed = 0.08;  // 0.02 ~ 0.1 m/s
    // 动态力度 (闭得越紧越大)
    double force = 100;  // 20 ~ 200 N

    // 构建ActionGoal消息
    robotiq_2f_gripper_msgs::CommandRobotiqGripperActionGoal cmd;
    cmd.goal.position = position_m;
    cmd.goal.speed = speed;
    cmd.goal.force = force;
    
    while (ros::ok())
    {
        right_pub_.publish(cmd);
        left_pub_.publish(cmd);
        loop_rate.sleep();
    }
    

    return 0;

}