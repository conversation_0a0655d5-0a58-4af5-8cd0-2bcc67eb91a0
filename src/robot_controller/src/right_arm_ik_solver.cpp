#include "robot_controller/right_arm_ik_solver.h"
#include <iostream>

RightArmIKSolver::RightArmIKSolver(const std::string& urdf_path, const std::string& end_effector_name)
    : end_effector_name_(end_effector_name)
    , initialized_(false)
    , damping_factor_(1e-6)
    , step_size_(0.1)
    , max_step_norm_(0.5)
    , use_joint_limits_(true)
{
    try {
        // 加载完整的机器人模型
        pinocchio::Model full_model;
        pinocchio::urdf::buildModel(urdf_path, full_model, false, true);

        // 构建右臂模型
        if (buildRightArmModel(full_model)) {
            // 检查末端执行器是否存在
            if (right_arm_model_.existFrame(end_effector_name_)) {
                end_effector_id_ = right_arm_model_.getFrameId(end_effector_name_);

                // 初始化关节限制（S1机器人右臂关节限制）
                q_min_.resize(right_arm_model_.nq);
                q_max_.resize(right_arm_model_.nq);

                // 设置默认关节角度限制（单位：弧度）
                q_min_ << -3.14159, -1.5708, -3.14159, -2.0944, -3.14159, -1.5708, -3.14159;  // 下限
                q_max_ <<  3.14159,  1.5708,  3.14159,  2.0944,  3.14159,  1.5708,  3.14159;  // 上限

                initialized_ = true;
                std::cout << "右臂逆运动学求解器初始化成功" << std::endl;
                std::cout << "右臂关节数量: " << right_arm_model_.nq << std::endl;
                std::cout << "末端执行器: " << end_effector_name_ << std::endl;
                std::cout << "关节限制已设置" << std::endl;
            } else {
                std::cerr << "错误：未找到末端执行器 " << end_effector_name_ << std::endl;
            }
        } else {
            std::cerr << "错误：构建右臂模型失败" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cerr << "初始化右臂逆运动学求解器时发生异常: " << e.what() << std::endl;
    }
}

RightArmIKSolver::~RightArmIKSolver() {
    // 析构函数
}

bool RightArmIKSolver::buildRightArmModel(const pinocchio::Model& full_model) {
    try {
        // 获取需要锁定的关节列表
        std::vector<pinocchio::JointIndex> joints_to_lock = getJointsToLock(full_model);

        // 设置锁定关节的位置为全0
        Eigen::VectorXd q_locked = Eigen::VectorXd::Zero(full_model.nq);

        // 构建简化模型（只包含右臂）
        right_arm_model_ = pinocchio::buildReducedModel(full_model, joints_to_lock, q_locked);
        right_arm_data_ = pinocchio::Data(right_arm_model_);

        return true;
    } catch (const std::exception& e) {
        std::cerr << "构建右臂模型时发生异常: " << e.what() << std::endl;
        return false;
    }
}

std::vector<pinocchio::JointIndex> RightArmIKSolver::getJointsToLock(const pinocchio::Model& full_model) {
    std::vector<std::string> joints_to_lock_names;
    
    // 添加要锁定的左臂关节
    joints_to_lock_names.push_back("l_joint1");
    joints_to_lock_names.push_back("l_joint2");
    joints_to_lock_names.push_back("l_joint3");
    joints_to_lock_names.push_back("l_joint4");
    joints_to_lock_names.push_back("l_joint4_mimic");
    joints_to_lock_names.push_back("l_joint5");
    joints_to_lock_names.push_back("l_joint6");
    joints_to_lock_names.push_back("l_joint7");

    // 添加要锁定的基座关节
    joints_to_lock_names.push_back("base_joint1");
    joints_to_lock_names.push_back("base_joint2");

    // 添加要锁定的左手关节
    joints_to_lock_names.push_back("l_index_MCP_FE");
    joints_to_lock_names.push_back("l_index_MCP_AA");
    joints_to_lock_names.push_back("l_index_PIP");
    joints_to_lock_names.push_back("l_index_DIP");
    joints_to_lock_names.push_back("l_middle_MCP_FE");
    joints_to_lock_names.push_back("l_middle_MCP_AA");
    joints_to_lock_names.push_back("l_middle_PIP");
    joints_to_lock_names.push_back("l_middle_DIP");
    joints_to_lock_names.push_back("l_picky_MCP_FE");
    joints_to_lock_names.push_back("l_picky_MCP_AA");
    joints_to_lock_names.push_back("l_picky_PIP");
    joints_to_lock_names.push_back("l_picky_DIP");
    joints_to_lock_names.push_back("l_ring_MCP_FE");
    joints_to_lock_names.push_back("l_ring_MCP_AA");
    joints_to_lock_names.push_back("l_ring_PIP");
    joints_to_lock_names.push_back("l_ring_DIP");
    joints_to_lock_names.push_back("l_thumb_MCP_FE");
    joints_to_lock_names.push_back("l_thumb_MCP_AA");
    joints_to_lock_names.push_back("l_thumb_PIP");
    joints_to_lock_names.push_back("l_thumb_DIP");

    // 添加要锁定的右手关节（只保留右臂7个关节）
    joints_to_lock_names.push_back("r_index_MCP_FE");
    joints_to_lock_names.push_back("r_index_MCP_AA");
    joints_to_lock_names.push_back("r_index_PIP");
    joints_to_lock_names.push_back("r_index_DIP");
    joints_to_lock_names.push_back("r_middle_MCP_FE");
    joints_to_lock_names.push_back("r_middle_MCP_AA");
    joints_to_lock_names.push_back("r_middle_PIP");
    joints_to_lock_names.push_back("r_middle_DIP");
    joints_to_lock_names.push_back("r_picky_MCP_FE");
    joints_to_lock_names.push_back("r_picky_MCP_AA");
    joints_to_lock_names.push_back("r_picky_PIP");
    joints_to_lock_names.push_back("r_picky_DIP");
    joints_to_lock_names.push_back("r_ring_MCP_FE");
    joints_to_lock_names.push_back("r_ring_MCP_AA");
    joints_to_lock_names.push_back("r_ring_PIP");
    joints_to_lock_names.push_back("r_ring_DIP");
    joints_to_lock_names.push_back("r_thumb_MCP_FE");
    joints_to_lock_names.push_back("r_thumb_MCP_AA");
    joints_to_lock_names.push_back("r_thumb_PIP");
    joints_to_lock_names.push_back("r_thumb_DIP");

    // 将关节名称转换为关节ID
    std::vector<pinocchio::JointIndex> joints_to_lock_ids;
    for (const auto& joint_name : joints_to_lock_names) {
        if (full_model.existJointName(joint_name)) {
            joints_to_lock_ids.push_back(full_model.getJointId(joint_name));
        }
    }

    return joints_to_lock_ids;
}



bool RightArmIKSolver::solvePositionIK(const Eigen::Vector3d& target_position,
                                       const Eigen::VectorXd& q_init,
                                       Eigen::VectorXd& q_solution,
                                       int max_iterations,
                                       double position_tolerance) {
    if (!initialized_) {
        std::cerr << "错误：求解器未正确初始化" << std::endl;
        return false;
    }

    if (q_init.size() != right_arm_model_.nq) {
        std::cerr << "错误：初始关节角度维度不匹配，期望 " << right_arm_model_.nq
                  << "，实际 " << q_init.size() << std::endl;
        return false;
    }

    q_solution = q_init;

    std::cout << "开始位置逆运动学求解，目标位置: " << target_position.transpose() << std::endl;

    for (int iter = 0; iter < max_iterations; ++iter) {
        // 计算当前关节角度下的正运动学
        pinocchio::forwardKinematics(right_arm_model_, right_arm_data_, q_solution);
        pinocchio::updateFramePlacements(right_arm_model_, right_arm_data_);

        // 获取当前末端位姿
        pinocchio::SE3 current_pose = right_arm_data_.oMf[end_effector_id_];

        // 计算位置误差
        Eigen::Vector3d pos_error = target_position - current_pose.translation();

        // 打印调试信息
        if (iter % 50 == 0) {
            std::cout << "迭代 " << iter << ": 位置误差 = " << pos_error.norm() << std::endl;
        }

        // 检查收敛条件（只考虑位置）
        if (pos_error.norm() < position_tolerance) {
            std::cout << "位置逆运动学求解成功，迭代次数: " << iter << std::endl;
            return true;  // 求解成功
        }

        // 计算雅可比矩阵（只取位置部分）
        Eigen::MatrixXd J_full(6, right_arm_model_.nv);
        pinocchio::computeFrameJacobian(right_arm_model_, right_arm_data_, q_solution,
                                       end_effector_id_, pinocchio::LOCAL_WORLD_ALIGNED, J_full);

        // 只使用位置部分的雅可比矩阵
        Eigen::MatrixXd J = J_full.topRows(3);

        // 使用阻尼最小二乘法求解关节速度
        Eigen::MatrixXd JtJ = J.transpose() * J;
        Eigen::MatrixXd damped_JtJ = JtJ + damping_factor_ * Eigen::MatrixXd::Identity(JtJ.rows(), JtJ.cols());
        Eigen::VectorXd dq = damped_JtJ.ldlt().solve(J.transpose() * pos_error);

        // 限制步长
        double step_norm = dq.norm();
        if (step_norm > max_step_norm_) {
            dq = dq * (max_step_norm_ / step_norm);
        }

        // 更新关节角度
        q_solution = pinocchio::integrate(right_arm_model_, q_solution, step_size_ * dq);
    }

    std::cout << "位置逆运动学求解未收敛，已达最大迭代次数: " << max_iterations << std::endl;
    return false;  // 未收敛
}

bool RightArmIKSolver::solveIK(const pinocchio::SE3& target_pose,
                               const Eigen::VectorXd& q_init,
                               Eigen::VectorXd& q_solution,
                               int max_iterations,
                               double position_tolerance,
                               double rotation_tolerance,
                               double position_weight,
                               double rotation_weight) {
    if (!initialized_) {
        std::cerr << "错误：求解器未正确初始化" << std::endl;
        return false;
    }

    if (q_init.size() != right_arm_model_.nq) {
        std::cerr << "错误：初始关节角度维度不匹配，期望 " << right_arm_model_.nq
                  << "，实际 " << q_init.size() << std::endl;
        return false;
    }

    q_solution = q_init;

    std::cout << "开始改进逆运动学求解，目标位置: " << target_pose.translation().transpose() << std::endl;

    // 自适应参数
    double adaptive_damping = damping_factor_;
    double adaptive_step_size = step_size_;
    double prev_error_norm = std::numeric_limits<double>::max();
    int stagnation_count = 0;

    for (int iter = 0; iter < max_iterations; ++iter) {
        // 计算当前关节角度下的正运动学
        pinocchio::forwardKinematics(right_arm_model_, right_arm_data_, q_solution);
        pinocchio::updateFramePlacements(right_arm_model_, right_arm_data_);

        // 获取当前末端位姿
        pinocchio::SE3 current_pose = right_arm_data_.oMf[end_effector_id_];

        // 计算位置误差
        Eigen::Vector3d pos_error = target_pose.translation() - current_pose.translation();

        // 计算旋转误差（使用更稳定的方法）
        Eigen::Matrix3d R_error = target_pose.rotation() * current_pose.rotation().transpose();
        Eigen::Vector3d rot_error = pinocchio::log3(R_error);

        // 组合误差向量，应用权重
        Eigen::VectorXd error(6);
        error.head<3>() = position_weight * pos_error;
        error.tail<3>() = rotation_weight * rot_error;

        double current_error_norm = error.norm();

        // 打印调试信息
        if (iter % 50 == 0) {
            std::cout << "迭代 " << iter << ": 位置误差 = " << pos_error.norm()
                      << ", 旋转误差 = " << rot_error.norm()
                      << ", 总误差 = " << current_error_norm << std::endl;
        }

        // 检查收敛条件
        if (pos_error.norm() < position_tolerance && rot_error.norm() < rotation_tolerance) {
            std::cout << "改进逆运动学求解成功，迭代次数: " << iter << std::endl;
            return true;  // 求解成功
        }

        // 自适应调整参数
        if (current_error_norm >= prev_error_norm) {
            stagnation_count++;
            if (stagnation_count > 5) {
                // 增加阻尼，减小步长
                adaptive_damping *= 2.0;
                adaptive_step_size *= 0.8;
                stagnation_count = 0;
                std::cout << "调整参数: damping=" << adaptive_damping
                          << ", step_size=" << adaptive_step_size << std::endl;
            }
        } else {
            stagnation_count = 0;
            // 逐渐恢复参数
            adaptive_damping = std::max(adaptive_damping * 0.95, damping_factor_);
            adaptive_step_size = std::min(adaptive_step_size * 1.02, step_size_);
        }
        prev_error_norm = current_error_norm;

        // 计算雅可比矩阵
        Eigen::MatrixXd J(6, right_arm_model_.nv);
        pinocchio::computeFrameJacobian(right_arm_model_, right_arm_data_, q_solution,
                                       end_effector_id_, pinocchio::LOCAL_WORLD_ALIGNED, J);

        // 应用权重到雅可比矩阵
        J.topRows(3) *= position_weight;
        J.bottomRows(3) *= rotation_weight;

        // 使用自适应阻尼最小二乘法
        Eigen::MatrixXd JtJ = J.transpose() * J;
        Eigen::MatrixXd damped_JtJ = JtJ + adaptive_damping * Eigen::MatrixXd::Identity(JtJ.rows(), JtJ.cols());

        // 使用更稳定的求解方法
        Eigen::VectorXd dq;
        Eigen::LDLT<Eigen::MatrixXd> solver(damped_JtJ);
        if (solver.info() == Eigen::Success) {
            dq = solver.solve(J.transpose() * error);
        } else {
            // 如果LDLT失败，使用SVD
            Eigen::JacobiSVD<Eigen::MatrixXd> svd(J, Eigen::ComputeThinU | Eigen::ComputeThinV);
            dq = svd.solve(error);
        }

        // 限制步长
        double step_norm = dq.norm();
        if (step_norm > max_step_norm_) {
            dq = dq * (max_step_norm_ / step_norm);
        }

        // 更新关节角度
        q_solution = pinocchio::integrate(right_arm_model_, q_solution, adaptive_step_size * dq);

        // 应用关节限制
        if (use_joint_limits_) {
            clampJointAngles(q_solution);
        }
    }

    std::cout << "改进逆运动学求解未收敛，已达最大迭代次数: " << max_iterations << std::endl;
    return false;  // 未收敛
}

pinocchio::SE3 RightArmIKSolver::computeForwardKinematics(const Eigen::VectorXd& q) {
    if (!initialized_) {
        std::cerr << "错误：求解器未正确初始化" << std::endl;
        return pinocchio::SE3::Identity();
    }

    if (q.size() != right_arm_model_.nq) {
        std::cerr << "错误：关节角度维度不匹配" << std::endl;
        return pinocchio::SE3::Identity();
    }

    pinocchio::forwardKinematics(right_arm_model_, right_arm_data_, q);
    pinocchio::updateFramePlacements(right_arm_model_, right_arm_data_);
    
    return right_arm_data_.oMf[end_effector_id_];
}

int RightArmIKSolver::getJointCount() const {
    return initialized_ ? right_arm_model_.nq : 0;
}

bool RightArmIKSolver::isInitialized() const {
    return initialized_;
}

void RightArmIKSolver::setParameters(double damping_factor, double step_size, double max_step_norm) {
    damping_factor_ = damping_factor;
    step_size_ = step_size;
    max_step_norm_ = max_step_norm;
}

void RightArmIKSolver::setJointLimits(const Eigen::VectorXd& q_min, const Eigen::VectorXd& q_max) {
    if (!initialized_) {
        std::cerr << "错误：求解器未初始化" << std::endl;
        return;
    }

    if (q_min.size() != right_arm_model_.nq || q_max.size() != right_arm_model_.nq) {
        std::cerr << "错误：关节限制维度不匹配" << std::endl;
        return;
    }

    q_min_ = q_min;
    q_max_ = q_max;
    use_joint_limits_ = true;

    std::cout << "关节限制已更新" << std::endl;
    std::cout << "下限: " << q_min_.transpose() << std::endl;
    std::cout << "上限: " << q_max_.transpose() << std::endl;
}

void RightArmIKSolver::getDefaultJointLimits(Eigen::VectorXd& q_min, Eigen::VectorXd& q_max) const {
    q_min.resize(7);
    q_max.resize(7);

    // S1机器人右臂关节限制（单位：弧度）
    q_min << -3.14159, -1.5708, -3.14159, -2.0944, -3.14159, -1.5708, -3.14159;  // 下限
    q_max <<  3.14159,  1.5708,  3.14159,  2.0944,  3.14159,  1.5708,  3.14159;  // 上限
}

bool RightArmIKSolver::isWithinJointLimits(const Eigen::VectorXd& q) const {
    if (!use_joint_limits_ || q.size() != q_min_.size()) {
        return true;
    }

    for (int i = 0; i < q.size(); ++i) {
        if (q(i) < q_min_(i) || q(i) > q_max_(i)) {
            return false;
        }
    }
    return true;
}

void RightArmIKSolver::clampJointAngles(Eigen::VectorXd& q) const {
    if (!use_joint_limits_ || q.size() != q_min_.size()) {
        return;
    }

    for (int i = 0; i < q.size(); ++i) {
        if (q(i) < q_min_(i)) {
            q(i) = q_min_(i);
        } else if (q(i) > q_max_(i)) {
            q(i) = q_max_(i);
        }
    }
}
