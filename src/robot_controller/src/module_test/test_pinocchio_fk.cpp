#include "robot_controller/def_class.h"


int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_pin_fk");
    ros::NodeHandle nh;
    
    ROS_INFO_STREAM("当前使用的 Pinocchio 版本" << PINOCCHIO_VERSION);
    
    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model, false, true);

    pinocchio::Data data(model);

    std::cout << "Total joints: " << model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << model.nv << std::endl;

    pinocchio::JointIndex jid;
    for(int i = 0; i < model.njoints; i++){
        jid = model.getJointId(model.names[i]);
        ROS_INFO_STREAM("joint " << i << ": " << model.names[i] << ", " << model.joints[i].nq() << " DOF");
    }

    Eigen::VectorXd q(model.nq);  // 机器人关节数
    q.setZero();  // 假设机器人所有关节都在零位置
    /*
    0: base_joint1, 1 DOF
    1: base_joint2, 1 DOF
    2: l_joint1, 1 DOF
    3: l_joint2, 1 DOF
    4: l_joint3, 1 DOF
    5: l_joint4, 1 DOF
    6: l_joint5, 1 DOF
    7: l_joint6, 1 DOF
    8: l_joint7, 1 DOF
    29: r_joint1, 1 DOF
    30: r_joint2, 1 DOF
    31: r_joint3, 1 DOF
    32: r_joint4, 1 DOF
    33: r_joint5, 1 DOF
    34: r_joint6, 1 DOF
    35: r_joint7, 1 DOF
    */
    q(29) = 0.308;
    q(30) = 0.688;
    q(31) = 0.295;
    q(32) = 0.457;
    q(33) = 0.161;
    q(34) = 0.326;
    q(35) = 0.598;

    pinocchio::FrameIndex fid = model.getFrameId("r_Link7");

    pinocchio::forwardKinematics(model, data, q);
    pinocchio::updateFramePlacements(model, data);
    
    // 获取末端执行器（例如手臂末端）的变换矩阵
    pinocchio::SE3Tpl<double> X_ee = data.oMf[fid];
    Eigen::Matrix4d X_matrix;
    // X_matrix.setIdentity();
    // X_matrix.block<3,3>(0,0) = X_ee.rotation();
    // X_matrix.block<3,1>(0,3) = X_ee.translation();

    // std::cout << "ee_link pose:\n" << X_matrix << std::endl;
    ROS_INFO_STREAM(X_ee);


    return 0;
}
