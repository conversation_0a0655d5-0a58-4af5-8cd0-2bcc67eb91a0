#include "robot_controller/right_arm_ik_solver.h"
#include <ros/ros.h>
#include <ros/package.h>
#include <iostream>

// 计算旋转误差的辅助函数
double computeRotationError(const Eigen::Matrix3d& R1, const Eigen::Matrix3d& R2) {
    Eigen::Matrix3d R_error = R1 * R2.transpose();
    Eigen::Vector3d rot_error = pinocchio::log3(R_error);
    return rot_error.norm();
}

// 创建测试位姿的辅助函数
pinocchio::SE3 createTestPose(const Eigen::Vector3d& position, 
                             const Eigen::Vector3d& rpy) {
    pinocchio::SE3 pose;
    pose.translation() = position;
    
    // 从RPY角度创建旋转矩阵
    double roll = rpy(0), pitch = rpy(1), yaw = rpy(2);
    Eigen::Matrix3d R;
    R = Eigen::AngleAxisd(yaw, Eigen::Vector3d::UnitZ()) *
        Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY()) *
        Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitX());
    pose.rotation() = R;
    
    return pose;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_improved_ik");
    ros::NodeHandle nh;
    
    std::cout << "=== 测试改进的逆运动学求解器 ===" << std::endl;
    
    // 获取URDF文件路径
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    
    // 创建右臂逆运动学求解器
    RightArmIKSolver ik_solver(path_urdf, "r_Link7");
    
    if (!ik_solver.isInitialized()) {
        std::cerr << "逆运动学求解器初始化失败" << std::endl;
        return -1;
    }
    
    // 测试1：已知关节角度的正运动学，然后逆运动学验证
    std::cout << "\n=== 测试1：已知解验证 ===" << std::endl;
    
    Eigen::VectorXd q_test(7);
    q_test << 0.308, 0.688, 0.295, 0.457, 0.161, 0.326, 0.598;
    
    std::cout << "原始关节角度: " << q_test.transpose() << std::endl;
    
    // 计算正运动学
    pinocchio::SE3 target_pose = ik_solver.computeForwardKinematics(q_test);
    std::cout << "目标位置: " << target_pose.translation().transpose() << std::endl;
    std::cout << "目标旋转矩阵:\n" << target_pose.rotation() << std::endl;
    
    // 使用改进的逆运动学求解
    Eigen::VectorXd q_init = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd q_solution;
    
    bool success = ik_solver.solveIK(target_pose, q_init, q_solution,
                                     1000, 1e-4, 1e-4, 1.0, 0.5);
    
    if (success) {
        std::cout << "求解成功！" << std::endl;
        std::cout << "求解关节角度: " << q_solution.transpose() << std::endl;
        
        // 验证解的准确性
        pinocchio::SE3 verify_pose = ik_solver.computeForwardKinematics(q_solution);
        Eigen::Vector3d pos_error = target_pose.translation() - verify_pose.translation();
        double rot_error = computeRotationError(target_pose.rotation(), verify_pose.rotation());
        
        std::cout << "验证结果:" << std::endl;
        std::cout << "位置误差: " << pos_error.transpose() << " (范数: " << pos_error.norm() << ")" << std::endl;
        std::cout << "旋转误差范数: " << rot_error << std::endl;
        
    } else {
        std::cout << "求解失败" << std::endl;
    }
    
    // 测试2：不同姿态的测试
    std::cout << "\n=== 测试2：不同姿态测试 ===" << std::endl;
    
    std::vector<std::pair<Eigen::Vector3d, Eigen::Vector3d>> test_poses = {
        {Eigen::Vector3d(0.5, -0.2, 0.8), Eigen::Vector3d(0, 0, 0)},           // 水平
        {Eigen::Vector3d(0.4, -0.15, 0.9), Eigen::Vector3d(0.3, 0, 0)},        // 轻微倾斜
        {Eigen::Vector3d(0.6, -0.25, 0.7), Eigen::Vector3d(0, 0.3, 0)},        // 俯仰
        {Eigen::Vector3d(0.45, -0.18, 0.85), Eigen::Vector3d(0, 0, 0.5)},      // 偏航
        {Eigen::Vector3d(0.35, -0.12, 0.95), Eigen::Vector3d(0.2, 0.2, 0.3)}   // 复合旋转
    };
    
    int success_count = 0;
    for (size_t i = 0; i < test_poses.size(); ++i) {
        std::cout << "\n--- 测试姿态 " << (i+1) << " ---" << std::endl;
        
        pinocchio::SE3 test_target = createTestPose(test_poses[i].first, test_poses[i].second);
        
        std::cout << "目标位置: " << test_target.translation().transpose() << std::endl;
        std::cout << "目标RPY: " << test_poses[i].second.transpose() << std::endl;
        
        Eigen::VectorXd q_test_init = Eigen::VectorXd::Zero(7);
        Eigen::VectorXd q_test_solution;
        
        bool test_success = ik_solver.solveIK(test_target, q_test_init, q_test_solution,
                                              1000, 1e-4, 1e-4, 1.0, 0.5);
        
        if (test_success) {
            success_count++;
            std::cout << "求解成功！" << std::endl;
            std::cout << "关节角度: " << q_test_solution.transpose() << std::endl;
            
            // 验证精度
            pinocchio::SE3 test_verify = ik_solver.computeForwardKinematics(q_test_solution);
            Eigen::Vector3d test_pos_error = test_target.translation() - test_verify.translation();
            double test_rot_error = computeRotationError(test_target.rotation(), test_verify.rotation());
            
            std::cout << "位置误差范数: " << test_pos_error.norm() << std::endl;
            std::cout << "旋转误差范数: " << test_rot_error << std::endl;
            
            // 检查是否满足高精度要求
            if (test_pos_error.norm() < 1e-3 && test_rot_error < 1e-2) {
                std::cout << "✓ 高精度要求满足" << std::endl;
            } else {
                std::cout << "⚠ 精度未达到要求" << std::endl;
            }
            
        } else {
            std::cout << "求解失败" << std::endl;
        }
    }
    
    std::cout << "\n=== 测试总结 ===" << std::endl;
    std::cout << "成功率: " << success_count << "/" << test_poses.size() 
              << " (" << (100.0 * success_count / test_poses.size()) << "%)" << std::endl;
    
    // 测试3：与原始方法对比
    std::cout << "\n=== 测试3：方法对比 ===" << std::endl;
    
    pinocchio::SE3 compare_target = createTestPose(Eigen::Vector3d(0.5, -0.2, 0.8), 
                                                  Eigen::Vector3d(0.2, 0.3, 0.4));
    
    Eigen::VectorXd q_compare_init = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd q_original, q_improved;
    
    std::cout << "原始方法:" << std::endl;
    bool original_success = ik_solver.solveIK(compare_target, q_compare_init, q_original, 
                                             500, 1e-3, 1e-3);
    
    std::cout << "\n改进方法:" << std::endl;
    bool improved_success = ik_solver.solveIK(compare_target, q_compare_init, q_improved,
                                              1000, 1e-4, 1e-4, 1.0, 0.5);
    
    if (original_success && improved_success) {
        pinocchio::SE3 original_result = ik_solver.computeForwardKinematics(q_original);
        pinocchio::SE3 improved_result = ik_solver.computeForwardKinematics(q_improved);
        
        double original_pos_error = (compare_target.translation() - original_result.translation()).norm();
        double original_rot_error = computeRotationError(compare_target.rotation(), original_result.rotation());
        
        double improved_pos_error = (compare_target.translation() - improved_result.translation()).norm();
        double improved_rot_error = computeRotationError(compare_target.rotation(), improved_result.rotation());
        
        std::cout << "\n对比结果:" << std::endl;
        std::cout << "原始方法 - 位置误差: " << original_pos_error << ", 旋转误差: " << original_rot_error << std::endl;
        std::cout << "改进方法 - 位置误差: " << improved_pos_error << ", 旋转误差: " << improved_rot_error << std::endl;
        
        if (improved_pos_error < original_pos_error && improved_rot_error < original_rot_error) {
            std::cout << "✓ 改进方法精度更高" << std::endl;
        }
    }
    
    std::cout << "\n=== 测试完成 ===" << std::endl;
    
    return 0;
}
