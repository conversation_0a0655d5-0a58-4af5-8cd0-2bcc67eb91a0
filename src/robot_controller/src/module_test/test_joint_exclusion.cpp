#include "robot_controller/def_class.h"
#include <pinocchio/algorithm/joint-configuration.hpp>
#include <pinocchio/spatial/explog.hpp>

/**
 * 通用函数：根据需要保留的关节名称列表，生成要锁定的关节名称列表
 * @param model Pinocchio模型
 * @param joints_to_keep 需要保留的关节名称列表
 * @return 需要锁定的关节名称列表
 */
std::vector<std::string> generateJointsToLock(const pinocchio::Model& model, 
                                             const std::vector<std::string>& joints_to_keep) {
    std::vector<std::string> joints_to_lock;
    
    // 遍历模型中的所有关节，排除需要保留的关节
    for(int i = 1; i < model.njoints; i++) {  // 从1开始，跳过universe关节
        std::string joint_name = model.names[i];
        
        // 检查当前关节是否在保留列表中
        bool should_keep = false;
        for(const std::string& keep_joint : joints_to_keep) {
            if(joint_name == keep_joint) {
                should_keep = true;
                break;
            }
        }
        
        // 如果不在保留列表中，则添加到锁定列表
        if(!should_keep) {
            joints_to_lock.push_back(joint_name);
        }
    }
    
    return joints_to_lock;
}

/**
 * 创建简化模型的通用函数
 * @param original_model 原始完整模型
 * @param joints_to_keep 需要保留的关节名称列表
 * @return 简化后的模型
 */
pinocchio::Model createReducedModel(const pinocchio::Model& original_model,
                                   const std::vector<std::string>& joints_to_keep) {
    // 生成要锁定的关节列表
    std::vector<std::string> joints_to_lock = generateJointsToLock(original_model, joints_to_keep);
    
    // 将关节名称转换为关节ID
    std::vector<pinocchio::JointIndex> joints_to_lock_ids;
    for(const std::string& joint_name : joints_to_lock) {
        if(original_model.existJointName(joint_name)) {
            joints_to_lock_ids.push_back(original_model.getJointId(joint_name));
        }
    }
    
    // 设置锁定关节的位置为全0
    Eigen::VectorXd q_lock = Eigen::VectorXd::Zero(original_model.nq);
    
    // 构建简化模型
    return pinocchio::buildReducedModel(original_model, joints_to_lock_ids, q_lock);
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_joint_exclusion");
    ros::NodeHandle nh;
    
    ROS_INFO_STREAM("测试关节排除法创建简化模型");

    // 加载完整模型
    pinocchio::Model full_model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, full_model, false, true);

    std::cout << "=== 原始完整模型信息 ===" << std::endl;
    std::cout << "Total joints: " << full_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << full_model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << full_model.nv << std::endl;

    // 打印所有关节名称
    std::cout << "\n所有关节列表:" << std::endl;
    for(int i = 0; i < full_model.njoints; i++){
        std::cout << "joint " << i << ": " << full_model.names[i] 
                  << ", " << full_model.joints[i].nq() << " DOF" << std::endl;
    }

    // 测试1: 创建只包含右臂的模型
    std::cout << "\n=== 测试1: 创建右臂模型 ===" << std::endl;
    std::vector<std::string> right_arm_joints = {
        "r_joint1", "r_joint2", "r_joint3", "r_joint4", 
        "r_joint5", "r_joint6", "r_joint7"
    };
    
    pinocchio::Model right_arm_model = createReducedModel(full_model, right_arm_joints);
    
    std::cout << "右臂模型信息:" << std::endl;
    std::cout << "Total joints: " << right_arm_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << right_arm_model.nq << std::endl;
    std::cout << "保留的关节:" << std::endl;
    for(int i = 0; i < right_arm_model.njoints; i++){
        std::cout << "  " << right_arm_model.names[i] << std::endl;
    }

    // 测试2: 创建只包含左臂的模型
    std::cout << "\n=== 测试2: 创建左臂模型 ===" << std::endl;
    std::vector<std::string> left_arm_joints = {
        "l_joint1", "l_joint2", "l_joint3", "l_joint4", 
        "l_joint5", "l_joint6", "l_joint7"
    };
    
    pinocchio::Model left_arm_model = createReducedModel(full_model, left_arm_joints);
    
    std::cout << "左臂模型信息:" << std::endl;
    std::cout << "Total joints: " << left_arm_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << left_arm_model.nq << std::endl;
    std::cout << "保留的关节:" << std::endl;
    for(int i = 0; i < left_arm_model.njoints; i++){
        std::cout << "  " << left_arm_model.names[i] << std::endl;
    }

    // 测试3: 创建双臂模型（不包含手指）
    std::cout << "\n=== 测试3: 创建双臂模型（不包含手指） ===" << std::endl;
    std::vector<std::string> dual_arm_joints = {
        "r_joint1", "r_joint2", "r_joint3", "r_joint4", "r_joint5", "r_joint6", "r_joint7",
        "l_joint1", "l_joint2", "l_joint3", "l_joint4", "l_joint5", "l_joint6", "l_joint7"
    };
    
    pinocchio::Model dual_arm_model = createReducedModel(full_model, dual_arm_joints);
    
    std::cout << "双臂模型信息:" << std::endl;
    std::cout << "Total joints: " << dual_arm_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << dual_arm_model.nq << std::endl;
    std::cout << "保留的关节:" << std::endl;
    for(int i = 0; i < dual_arm_model.njoints; i++){
        std::cout << "  " << dual_arm_model.names[i] << std::endl;
    }

    // 测试4: 创建包含基座的右臂模型
    std::cout << "\n=== 测试4: 创建包含基座的右臂模型 ===" << std::endl;
    std::vector<std::string> base_right_arm_joints = {
        "base_joint1", "base_joint2",
        "r_joint1", "r_joint2", "r_joint3", "r_joint4", "r_joint5", "r_joint6", "r_joint7"
    };
    
    pinocchio::Model base_right_arm_model = createReducedModel(full_model, base_right_arm_joints);
    
    std::cout << "基座+右臂模型信息:" << std::endl;
    std::cout << "Total joints: " << base_right_arm_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << base_right_arm_model.nq << std::endl;
    std::cout << "保留的关节:" << std::endl;
    for(int i = 0; i < base_right_arm_model.njoints; i++){
        std::cout << "  " << base_right_arm_model.names[i] << std::endl;
    }

    // 演示如何查看被锁定的关节
    std::cout << "\n=== 演示: 查看被锁定的关节 ===" << std::endl;
    std::vector<std::string> locked_joints = generateJointsToLock(full_model, right_arm_joints);
    std::cout << "右臂模型中被锁定的关节数量: " << locked_joints.size() << std::endl;
    std::cout << "被锁定的关节列表:" << std::endl;
    for(const std::string& joint : locked_joints) {
        std::cout << "  " << joint << std::endl;
    }

    return 0;
}
