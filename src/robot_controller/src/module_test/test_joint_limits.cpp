#include "robot_controller/right_arm_ik_solver.h"
#include <ros/ros.h>
#include <ros/package.h>
#include <iostream>

// 创建测试位姿的辅助函数
pinocchio::SE3 createTestPose(const Eigen::Vector3d& position, 
                             const Eigen::Vector3d& rpy) {
    pinocchio::SE3 pose;
    pose.translation() = position;
    
    // 从RPY角度创建旋转矩阵
    double roll = rpy(0), pitch = rpy(1), yaw = rpy(2);
    Eigen::Matrix3d R;
    R = Eigen::AngleAxisd(yaw, Eigen::Vector3d::UnitZ()) *
        Eigen::AngleAxisd(pitch, Eigen::Vector3d::UnitY()) *
        Eigen::AngleAxisd(roll, Eigen::Vector3d::UnitX());
    pose.rotation() = R;
    
    return pose;
}

int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_joint_limits");
    ros::NodeHandle nh;
    
    std::cout << "=== 测试关节限制功能 ===" << std::endl;
    
    // 获取URDF文件路径
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    
    // 创建右臂逆运动学求解器
    RightArmIKSolver ik_solver(path_urdf, "r_Link7");
    
    if (!ik_solver.isInitialized()) {
        std::cerr << "逆运动学求解器初始化失败" << std::endl;
        return -1;
    }
    
    // 测试1：显示默认关节限制
    std::cout << "\n=== 测试1：默认关节限制 ===" << std::endl;
    
    Eigen::VectorXd default_q_min, default_q_max;
    ik_solver.getDefaultJointLimits(default_q_min, default_q_max);
    
    std::cout << "默认关节限制:" << std::endl;
    std::cout << "下限 (度): ";
    for (int i = 0; i < default_q_min.size(); ++i) {
        std::cout << (default_q_min(i) * 180.0 / M_PI) << " ";
    }
    std::cout << std::endl;
    
    std::cout << "上限 (度): ";
    for (int i = 0; i < default_q_max.size(); ++i) {
        std::cout << (default_q_max(i) * 180.0 / M_PI) << " ";
    }
    std::cout << std::endl;
    
    // 测试2：设置自定义关节限制
    std::cout << "\n=== 测试2：自定义关节限制 ===" << std::endl;
    
    Eigen::VectorXd custom_q_min(7), custom_q_max(7);
    // 设置更严格的关节限制（单位：弧度）
    custom_q_min << -2.0, -1.0, -2.0, -1.5, -2.0, -1.0, -2.0;
    custom_q_max <<  2.0,  1.0,  2.0,  1.5,  2.0,  1.0,  2.0;
    
    ik_solver.setJointLimits(custom_q_min, custom_q_max);
    
    // 测试3：在限制范围内的逆运动学求解
    std::cout << "\n=== 测试3：限制范围内求解 ===" << std::endl;
    
    pinocchio::SE3 target_pose = createTestPose(Eigen::Vector3d(0.4, -0.15, 0.8), 
                                               Eigen::Vector3d(0.1, 0.1, 0.1));
    
    std::cout << "目标位置: " << target_pose.translation().transpose() << std::endl;
    
    Eigen::VectorXd q_init = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd q_solution;
    
    bool success = ik_solver.solveIK(target_pose, q_init, q_solution);
    
    if (success) {
        std::cout << "求解成功！" << std::endl;
        std::cout << "关节角度 (度): ";
        for (int i = 0; i < q_solution.size(); ++i) {
            std::cout << (q_solution(i) * 180.0 / M_PI) << " ";
        }
        std::cout << std::endl;
        
        // 检查是否在限制范围内
        bool within_limits = ik_solver.isWithinJointLimits(q_solution);
        std::cout << "是否在关节限制范围内: " << (within_limits ? "是" : "否") << std::endl;
        
        // 验证解的准确性
        pinocchio::SE3 verify_pose = ik_solver.computeForwardKinematics(q_solution);
        Eigen::Vector3d pos_error = target_pose.translation() - verify_pose.translation();
        std::cout << "位置误差范数: " << pos_error.norm() << std::endl;
        
    } else {
        std::cout << "求解失败" << std::endl;
    }
    
    // 测试4：测试极限位置
    std::cout << "\n=== 测试4：极限位置测试 ===" << std::endl;
    
    std::vector<Eigen::Vector3d> extreme_positions = {
        Eigen::Vector3d(0.7, -0.3, 0.6),   // 远距离
        Eigen::Vector3d(0.2, -0.1, 1.1),   // 高位置
        Eigen::Vector3d(0.6, -0.4, 0.5),   // 侧面极限
    };
    
    for (size_t i = 0; i < extreme_positions.size(); ++i) {
        std::cout << "\n--- 极限位置 " << (i+1) << " ---" << std::endl;
        
        pinocchio::SE3 extreme_target = createTestPose(extreme_positions[i], 
                                                      Eigen::Vector3d(0, 0, 0));
        
        std::cout << "目标位置: " << extreme_target.translation().transpose() << std::endl;
        
        Eigen::VectorXd q_extreme_init = Eigen::VectorXd::Zero(7);
        Eigen::VectorXd q_extreme_solution;
        
        bool extreme_success = ik_solver.solveIK(extreme_target, q_extreme_init, q_extreme_solution);
        
        if (extreme_success) {
            std::cout << "求解成功！" << std::endl;
            
            // 检查关节限制
            bool extreme_within_limits = ik_solver.isWithinJointLimits(q_extreme_solution);
            std::cout << "关节限制检查: " << (extreme_within_limits ? "通过" : "超限") << std::endl;
            
            // 显示关节角度
            std::cout << "关节角度 (度): ";
            for (int j = 0; j < q_extreme_solution.size(); ++j) {
                double angle_deg = q_extreme_solution(j) * 180.0 / M_PI;
                std::cout << angle_deg << " ";
            }
            std::cout << std::endl;
            
            // 验证精度
            pinocchio::SE3 extreme_verify = ik_solver.computeForwardKinematics(q_extreme_solution);
            Eigen::Vector3d extreme_error = extreme_target.translation() - extreme_verify.translation();
            std::cout << "位置误差范数: " << extreme_error.norm() << std::endl;
            
        } else {
            std::cout << "求解失败（可能超出工作空间或关节限制）" << std::endl;
        }
    }
    
    // 测试5：恢复默认限制
    std::cout << "\n=== 测试5：恢复默认限制 ===" << std::endl;
    
    ik_solver.setJointLimits(default_q_min, default_q_max);
    std::cout << "已恢复默认关节限制" << std::endl;
    
    // 重新测试之前失败的位置
    pinocchio::SE3 retry_target = createTestPose(Eigen::Vector3d(0.6, -0.4, 0.5), 
                                                Eigen::Vector3d(0, 0, 0));
    
    Eigen::VectorXd q_retry_init = Eigen::VectorXd::Zero(7);
    Eigen::VectorXd q_retry_solution;
    
    bool retry_success = ik_solver.solveIK(retry_target, q_retry_init, q_retry_solution);
    
    if (retry_success) {
        std::cout << "使用默认限制求解成功！" << std::endl;
        std::cout << "关节角度 (度): ";
        for (int i = 0; i < q_retry_solution.size(); ++i) {
            std::cout << (q_retry_solution(i) * 180.0 / M_PI) << " ";
        }
        std::cout << std::endl;
        
        pinocchio::SE3 retry_verify = ik_solver.computeForwardKinematics(q_retry_solution);
        Eigen::Vector3d retry_error = retry_target.translation() - retry_verify.translation();
        std::cout << "位置误差范数: " << retry_error.norm() << std::endl;
        
    } else {
        std::cout << "即使使用默认限制也求解失败" << std::endl;
    }
    
    std::cout << "\n=== 关节限制测试完成 ===" << std::endl;
    
    return 0;
}
