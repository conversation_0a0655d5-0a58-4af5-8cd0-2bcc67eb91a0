#include "robot_controller/def_class.h"
#include <pinocchio/algorithm/joint-configuration.hpp>
#include <pinocchio/spatial/explog.hpp>


int main(int argc, char *argv[])
{
    setlocale(LC_ALL,"");
    ros::init(argc, argv, "test_pin_fk2");
    ros::NodeHandle nh;
    
    ROS_INFO_STREAM("当前使用的 Pinocchio 版本" << PINOCCHIO_VERSION);

    pinocchio::Model model;
    std::string path_pkg = ros::package::getPath("robot_controller");
    std::string path_urdf = path_pkg + "/description/urdf/S1_robot.urdf";
    pinocchio::urdf::buildModel(path_urdf, model, false, true);

    std::cout << "=== 原始完整模型信息 ===" << std::endl;
    std::cout << "Total joints: " << model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << model.nv << std::endl;

    // 打印所有关节名称，便于了解模型结构
    std::cout << "\n所有关节列表:" << std::endl;
    pinocchio::JointIndex jid;
    for(int i = 0; i < model.njoints; i++){
        jid = model.getJointId(model.names[i]);
        std::cout << "joint " << i << ": " << model.names[i] << ", " << model.joints[i].nq() << " DOF" << std::endl;
    }

    // 构建只包含右臂的模型
    // 通过排除法确定要锁定的关节：定义需要保留的关节，其余全部锁定
    std::vector<std::string> joints_to_keep;

    // 只保留右臂的7个关节
    joints_to_keep.push_back("r_joint1");
    joints_to_keep.push_back("r_joint2");
    joints_to_keep.push_back("r_joint3");
    joints_to_keep.push_back("r_joint4");
    joints_to_keep.push_back("r_joint4_mimic");
    joints_to_keep.push_back("r_joint5");
    joints_to_keep.push_back("r_joint6");
    joints_to_keep.push_back("r_joint7");

    // 通过排除法生成要锁定的关节列表
    std::vector<std::string> list_of_joints_to_lock_by_name;

    // 遍历模型中的所有关节，排除需要保留的关节
    for(int i = 1; i < model.njoints; i++) {  // 从1开始，跳过universe关节
        std::string joint_name = model.names[i];

        // 检查当前关节是否在保留列表中
        bool should_keep = false;
        for(const std::string& keep_joint : joints_to_keep) {
            if(joint_name == keep_joint) {
                should_keep = true;
                break;
            }
        }

        // 如果不在保留列表中，则添加到锁定列表
        if(!should_keep) {
            list_of_joints_to_lock_by_name.push_back(joint_name);
        }
    }

    std::cout << "\n=== 准备锁定的关节 ===" << std::endl;
    // 将关节名称转换为关节ID
    std::vector<pinocchio::JointIndex> list_of_joints_to_lock_by_id;
    for (std::vector<std::string>::const_iterator it = list_of_joints_to_lock_by_name.begin();
        it != list_of_joints_to_lock_by_name.end(); ++it)
    {
        const std::string & joint_name = *it;
        if (model.existJointName(joint_name)) {
            list_of_joints_to_lock_by_id.push_back(model.getJointId(joint_name));
            std::cout << "将锁定关节: " << joint_name << std::endl;
        } else {
            std::cout << "关节 " << joint_name << " 不存在于模型中" << std::endl;
        }
    }

    // 设置锁定关节的位置为全0
    Eigen::VectorXd q_rand = Eigen::VectorXd::Zero(model.nq);

    // 构建简化模型（只包含右臂）
    pinocchio::Model right_arm_model = pinocchio::buildReducedModel(model, list_of_joints_to_lock_by_id, q_rand);

    std::cout << "\n=== 右臂模型信息 ===" << std::endl;
    std::cout << "Total joints: " << right_arm_model.njoints << std::endl;
    std::cout << "Total DOF (nq): " << right_arm_model.nq << std::endl;
    std::cout << "Total velocity DOF (nv): " << right_arm_model.nv << std::endl;
    std::cout << "length of names: " << right_arm_model.names.size() << std::endl;
    std::cout << "length of joints: " << right_arm_model.joints.size() << std::endl;
    std::cout << "length of lowerPositionLimit: " << right_arm_model.lowerPositionLimit.size() << std::endl;

    std::cout << "\n右臂模型关节列表:" << std::endl;
    for(int i = 0; i < right_arm_model.njoints; i++){
        std::cout << "joint " << i << ": " << right_arm_model.names[i] << ", " << right_arm_model.joints[i].nq() << " DOF" << std::endl;
    }

    // 创建右臂模型的数据结构
    pinocchio::Data right_arm_data(right_arm_model);

    // 测试右臂模型的正运动学
    std::cout << "\n=== 测试右臂正运动学 ===" << std::endl;

    // 为右臂模型设置关节角度
    Eigen::VectorXd q_right(right_arm_model.nq);
    q_right.setZero();  // 初始化为零位置

    // 设置一些测试角度（根据右臂模型的实际关节数量）
    if(right_arm_model.nq >= 7) {
        // 假设前7个关节是右臂的7个关节
        q_right(0) = 0.308;  // r_joint1
        q_right(1) = 0.688;  // r_joint2
        q_right(2) = 0.295;  // r_joint3
        q_right(3) = 0.457;  // r_joint4
        q_right(4) = 0.161;  // r_joint5
        q_right(5) = 0.326;  // r_joint6
        q_right(6) = 0.598;  // r_joint7
    }

    std::cout << "设置的右臂关节角度: ";
    for(int i = 0; i < q_right.size(); i++) {
        std::cout << q_right(i) << " ";
    }
    std::cout << std::endl;

    // 查找右臂末端执行器frame
    std::string end_effector_name = "r_Link7";
    if(right_arm_model.existFrame(end_effector_name)) {
        pinocchio::FrameIndex fid = right_arm_model.getFrameId(end_effector_name);

        // 执行正运动学计算
        pinocchio::forwardKinematics(right_arm_model, right_arm_data, q_right);
        pinocchio::updateFramePlacements(right_arm_model, right_arm_data);

        // 获取末端执行器的变换矩阵
        pinocchio::SE3Tpl<double> X_ee = right_arm_data.oMf[fid];

        std::cout << "\n右臂末端执行器位姿:" << std::endl;
        std::cout << "位置 (x, y, z): " << X_ee.translation().transpose() << std::endl;
        std::cout << "旋转矩阵:\n" << X_ee.rotation() << std::endl;

        // 转换为4x4变换矩阵
        Eigen::Matrix4d X_matrix = Eigen::Matrix4d::Identity();
        X_matrix.block<3,3>(0,0) = X_ee.rotation();
        X_matrix.block<3,1>(0,3) = X_ee.translation();

        std::cout << "\n4x4变换矩阵:\n" << X_matrix << std::endl;

    } else {
        std::cout << "未找到右臂末端执行器frame: " << end_effector_name << std::endl;
        std::cout << "可用的frames:" << std::endl;
        for(int i = 0; i < right_arm_model.nframes; i++) {
            std::cout << "  " << right_arm_model.frames[i].name << std::endl;
        }
        return -1;
    }

    // ===== 添加逆运动学求解部分 =====
    std::cout << "\n=== 测试右臂逆运动学 ===" << std::endl;

    // 设置目标位姿（使用刚才正运动学计算得到的位姿作为目标）
    pinocchio::SE3Tpl<double> target_pose = right_arm_data.oMf[right_arm_model.getFrameId(end_effector_name)];

    std::cout << "目标位姿:" << std::endl;
    std::cout << "位置 (x, y, z): " << target_pose.translation().transpose() << std::endl;
    std::cout << "旋转矩阵:\n" << target_pose.rotation() << std::endl;

    // 逆运动学求解参数
    const double eps = 1e-3;           // 位置精度（放宽一些）
    const double eps_rot = 1e-3;       // 旋转精度（放宽一些）
    const int max_iter = 500;          // 最大迭代次数
    const double dt = 0.1;             // 时间步长
    const double damping = 1e-6;       // 阻尼系数，用于数值稳定性

    // 初始关节角度（使用接近目标的初始值）
    Eigen::VectorXd q_init = Eigen::VectorXd::Zero(right_arm_model.nq);

    // 使用接近正运动学解的初始值
    if(right_arm_model.nq >= 7) {
        q_init(0) = 0.2;   // r_joint1
        q_init(1) = 0.5;   // r_joint2
        q_init(2) = 0.2;   // r_joint3
        q_init(3) = 0.4;   // r_joint4
        q_init(4) = 0.1;   // r_joint5
        q_init(5) = 0.3;   // r_joint6
        q_init(6) = 0.4;   // r_joint7
    }

    std::cout << "初始关节角度: ";
    for(int i = 0; i < q_init.size(); i++) {
        std::cout << q_init(i) << " ";
    }
    std::cout << std::endl;

    // 使用数值方法求解逆运动学
    Eigen::VectorXd q_sol = q_init;  // 解的初始值
    pinocchio::Data ik_data(right_arm_model);  // 用于逆运动学计算的数据结构

    bool converged = false;
    int iter = 0;

    for(iter = 0; iter < max_iter; iter++) {
        // 计算当前关节角度下的正运动学
        pinocchio::forwardKinematics(right_arm_model, ik_data, q_sol);
        pinocchio::updateFramePlacements(right_arm_model, ik_data);

        // 获取当前末端位姿
        pinocchio::SE3Tpl<double> current_pose = ik_data.oMf[right_arm_model.getFrameId(end_effector_name)];

        // 计算位置误差
        Eigen::Vector3d pos_error = target_pose.translation() - current_pose.translation();

        // 计算旋转误差（使用对数映射）
        pinocchio::SE3Tpl<double> error_pose = target_pose.actInv(current_pose);
        Eigen::Vector3d rot_error = pinocchio::log3(error_pose.rotation());

        // 组合位置和旋转误差
        Eigen::VectorXd error(6);
        error.head<3>() = pos_error;
        error.tail<3>() = rot_error;

        // 检查收敛条件
        if(pos_error.norm() < eps && rot_error.norm() < eps_rot) {
            converged = true;
            break;
        }

        // 计算雅可比矩阵
        Eigen::MatrixXd J(6, right_arm_model.nv);
        pinocchio::computeFrameJacobian(right_arm_model, ik_data, q_sol,
                                       right_arm_model.getFrameId(end_effector_name),
                                       pinocchio::LOCAL_WORLD_ALIGNED, J);

        // 使用阻尼最小二乘法求解关节速度（更稳定）
        Eigen::MatrixXd JtJ = J.transpose() * J;
        Eigen::MatrixXd damped_JtJ = JtJ + damping * Eigen::MatrixXd::Identity(JtJ.rows(), JtJ.cols());
        Eigen::VectorXd dq = damped_JtJ.ldlt().solve(J.transpose() * error);

        // 限制步长以避免过大的跳跃
        double step_norm = dq.norm();
        if(step_norm > 0.5) {
            dq = dq * (0.5 / step_norm);
        }

        // 更新关节角度
        q_sol = pinocchio::integrate(right_arm_model, q_sol, dt * dq);

        // 打印迭代信息（每100次迭代打印一次）
        if(iter % 100 == 0) {
            std::cout << "迭代 " << iter << ": 位置误差 = " << pos_error.norm()
                      << ", 旋转误差 = " << rot_error.norm() << std::endl;
        }
    }

    // 输出逆运动学求解结果
    std::cout << "\n=== 逆运动学求解结果 ===" << std::endl;
    if(converged) {
        std::cout << "逆运动学求解成功！" << std::endl;
        std::cout << "迭代次数: " << iter << std::endl;
    } else {
        std::cout << "逆运动学求解未收敛，已达最大迭代次数: " << max_iter << std::endl;
    }

    std::cout << "求解的关节角度: ";
    for(int i = 0; i < q_sol.size(); i++) {
        std::cout << q_sol(i) << " ";
    }
    std::cout << std::endl;

    // 验证逆运动学解的准确性
    std::cout << "\n=== 验证逆运动学解 ===" << std::endl;
    pinocchio::forwardKinematics(right_arm_model, ik_data, q_sol);
    pinocchio::updateFramePlacements(right_arm_model, ik_data);
    pinocchio::SE3Tpl<double> final_pose = ik_data.oMf[right_arm_model.getFrameId(end_effector_name)];

    std::cout << "求解后的末端位姿:" << std::endl;
    std::cout << "位置 (x, y, z): " << final_pose.translation().transpose() << std::endl;
    std::cout << "旋转矩阵:\n" << final_pose.rotation() << std::endl;

    // 计算最终误差
    Eigen::Vector3d final_pos_error = target_pose.translation() - final_pose.translation();
    pinocchio::SE3Tpl<double> final_error_pose = target_pose.actInv(final_pose);
    Eigen::Vector3d final_rot_error = pinocchio::log3(final_error_pose.rotation());

    std::cout << "\n最终误差:" << std::endl;
    std::cout << "位置误差: " << final_pos_error.transpose() << " (范数: " << final_pos_error.norm() << ")" << std::endl;
    std::cout << "旋转误差: " << final_rot_error.transpose() << " (范数: " << final_rot_error.norm() << ")" << std::endl;

    // ===== 测试不同目标位姿的逆运动学 =====
    std::cout << "\n=== 测试不同目标位姿的逆运动学 ===" << std::endl;

    // 定义一个新的目标位姿
    pinocchio::SE3Tpl<double> new_target;
    new_target.translation() << 0.5, -0.2, 0.8;  // 新的位置
    new_target.rotation() = Eigen::Matrix3d::Identity();  // 单位旋转矩阵

    std::cout << "新目标位姿:" << std::endl;
    std::cout << "位置 (x, y, z): " << new_target.translation().transpose() << std::endl;
    std::cout << "旋转矩阵:\n" << new_target.rotation() << std::endl;

    // 使用零位置作为初始值
    Eigen::VectorXd q_new_init = Eigen::VectorXd::Zero(right_arm_model.nq);

    // 逆运动学求解
    Eigen::VectorXd q_new_sol = q_new_init;
    pinocchio::Data new_ik_data(right_arm_model);

    bool new_converged = false;
    int new_iter = 0;

    for(new_iter = 0; new_iter < max_iter; new_iter++) {
        // 计算当前关节角度下的正运动学
        pinocchio::forwardKinematics(right_arm_model, new_ik_data, q_new_sol);
        pinocchio::updateFramePlacements(right_arm_model, new_ik_data);

        // 获取当前末端位姿
        pinocchio::SE3Tpl<double> new_current_pose = new_ik_data.oMf[right_arm_model.getFrameId(end_effector_name)];

        // 计算位置误差
        Eigen::Vector3d new_pos_error = new_target.translation() - new_current_pose.translation();

        // 计算旋转误差
        pinocchio::SE3Tpl<double> new_error_pose = new_target.actInv(new_current_pose);
        Eigen::Vector3d new_rot_error = pinocchio::log3(new_error_pose.rotation());

        // 组合位置和旋转误差
        Eigen::VectorXd new_error(6);
        new_error.head<3>() = new_pos_error;
        new_error.tail<3>() = new_rot_error;

        // 检查收敛条件
        if(new_pos_error.norm() < eps && new_rot_error.norm() < eps_rot) {
            new_converged = true;
            break;
        }

        // 计算雅可比矩阵
        Eigen::MatrixXd new_J(6, right_arm_model.nv);
        pinocchio::computeFrameJacobian(right_arm_model, new_ik_data, q_new_sol,
                                       right_arm_model.getFrameId(end_effector_name),
                                       pinocchio::LOCAL_WORLD_ALIGNED, new_J);

        // 使用阻尼最小二乘法求解关节速度
        Eigen::MatrixXd new_JtJ = new_J.transpose() * new_J;
        Eigen::MatrixXd new_damped_JtJ = new_JtJ + damping * Eigen::MatrixXd::Identity(new_JtJ.rows(), new_JtJ.cols());
        Eigen::VectorXd new_dq = new_damped_JtJ.ldlt().solve(new_J.transpose() * new_error);

        // 限制步长
        double new_step_norm = new_dq.norm();
        if(new_step_norm > 0.5) {
            new_dq = new_dq * (0.5 / new_step_norm);
        }

        // 更新关节角度
        q_new_sol = pinocchio::integrate(right_arm_model, q_new_sol, dt * new_dq);

        // 打印迭代信息
        if(new_iter % 100 == 0) {
            std::cout << "迭代 " << new_iter << ": 位置误差 = " << new_pos_error.norm()
                      << ", 旋转误差 = " << new_rot_error.norm() << std::endl;
        }
    }

    // 输出新目标的逆运动学求解结果
    std::cout << "\n新目标逆运动学求解结果:" << std::endl;
    if(new_converged) {
        std::cout << "逆运动学求解成功！" << std::endl;
        std::cout << "迭代次数: " << new_iter << std::endl;
    } else {
        std::cout << "逆运动学求解未收敛，已达最大迭代次数: " << max_iter << std::endl;
    }

    std::cout << "求解的关节角度: ";
    for(int i = 0; i < q_new_sol.size(); i++) {
        std::cout << q_new_sol(i) << " ";
    }
    std::cout << std::endl;

    // 验证新目标的逆运动学解
    pinocchio::forwardKinematics(right_arm_model, new_ik_data, q_new_sol);
    pinocchio::updateFramePlacements(right_arm_model, new_ik_data);
    pinocchio::SE3Tpl<double> new_final_pose = new_ik_data.oMf[right_arm_model.getFrameId(end_effector_name)];

    std::cout << "\n验证新目标求解后的末端位姿:" << std::endl;
    std::cout << "位置 (x, y, z): " << new_final_pose.translation().transpose() << std::endl;
    std::cout << "旋转矩阵:\n" << new_final_pose.rotation() << std::endl;

    // 计算新目标的最终误差
    Eigen::Vector3d new_final_pos_error = new_target.translation() - new_final_pose.translation();
    pinocchio::SE3Tpl<double> new_final_error_pose = new_target.actInv(new_final_pose);
    Eigen::Vector3d new_final_rot_error = pinocchio::log3(new_final_error_pose.rotation());

    std::cout << "\n新目标最终误差:" << std::endl;
    std::cout << "位置误差: " << new_final_pos_error.transpose() << " (范数: " << new_final_pos_error.norm() << ")" << std::endl;
    std::cout << "旋转误差: " << new_final_rot_error.transpose() << " (范数: " << new_final_rot_error.norm() << ")" << std::endl;

    return 0;
}
