# generated from catkin/cmake/template/order_packages.context.py.in
source_root_dir = '/home/<USER>/workspace/S1_robot/src'
whitelisted_packages = 'robot_controller'.split(';') if 'robot_controller' != '' else []
blacklisted_packages = ''.split(';') if '' != '' else []
underlay_workspaces = '/home/<USER>/workspace/S1_robot/devel;/opt/ros/noetic'.split(';') if '/home/<USER>/workspace/S1_robot/devel;/opt/ros/noetic' != '' else []
