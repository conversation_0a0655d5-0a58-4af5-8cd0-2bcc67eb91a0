Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_81489/fast && /usr/bin/make  -f CMakeFiles/cmTC_81489.dir/build.make CMakeFiles/cmTC_81489.dir/build
make[1]: Entering directory '/home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_81489.dir/src.c.o
/usr/bin/cc -DCMAKE_HAVE_LIBC_PTHREAD   -o CMakeFiles/cmTC_81489.dir/src.c.o -c /home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_81489
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_81489.dir/link.txt --verbose=1
/usr/bin/cc -rdynamic CMakeFiles/cmTC_81489.dir/src.c.o -o cmTC_81489 
/usr/bin/ld: CMakeFiles/cmTC_81489.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x5e): undefined reference to `pthread_cancel'
/usr/bin/ld: src.c:(.text+0x6f): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_81489.dir/build.make:99: cmTC_81489] Error 1
make[1]: Leaving directory '/home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:127: cmTC_81489/fast] Error 2


Source file was:
#include <pthread.h>

static void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_cancel(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make -f Makefile cmTC_3c4c7/fast && /usr/bin/make  -f CMakeFiles/cmTC_3c4c7.dir/build.make CMakeFiles/cmTC_3c4c7.dir/build
make[1]: Entering directory '/home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_3c4c7.dir/CheckFunctionExists.c.o
/usr/bin/cc   -DCHECK_FUNCTION_EXISTS=pthread_create -o CMakeFiles/cmTC_3c4c7.dir/CheckFunctionExists.c.o -c /usr/local/share/cmake-3.24/Modules/CheckFunctionExists.c
Linking C executable cmTC_3c4c7
/usr/local/bin/cmake -E cmake_link_script CMakeFiles/cmTC_3c4c7.dir/link.txt --verbose=1
/usr/bin/cc  -DCHECK_FUNCTION_EXISTS=pthread_create -rdynamic CMakeFiles/cmTC_3c4c7.dir/CheckFunctionExists.c.o -o cmTC_3c4c7  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_3c4c7.dir/build.make:99: cmTC_3c4c7] Error 1
make[1]: Leaving directory '/home/<USER>/workspace/S1_robot/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:127: cmTC_3c4c7/fast] Error 2



