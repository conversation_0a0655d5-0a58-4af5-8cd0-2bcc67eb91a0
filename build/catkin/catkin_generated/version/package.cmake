set(_CATKIN_CURRENT_PACKAGE "catkin")
set(catkin_VERSION "0.8.12")
set(catkin_MAINTAINER "<PERSON> <<EMAIL>>, <PERSON> <i<PERSON><PERSON><PERSON>@ekumenlabs.com>")
set(catkin_PACKAGE_FORMAT "3")
set(catkin_BUILD_DEPENDS "python-argparse" "python-catkin-pkg" "python3-catkin-pkg" "python-empy" "python3-empy")
set(catkin_BUILD_DEPENDS_python-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_BUILD_DEPENDS_python3-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_BUILD_EXPORT_DEPENDS "google-mock" "gtest" "python-nose" "python3-nose" "python-argparse" "python-catkin-pkg" "python3-catkin-pkg" "python-empy" "python3-empy")
set(catkin_BUILD_EXPORT_DEPENDS_python-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_BUILD_EXPORT_DEPENDS_python3-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_BUILDTOOL_DEPENDS "cmake" "python-setuptools" "python3-setuptools")
set(catkin_BUILDTOOL_EXPORT_DEPENDS "cmake" "python3-setuptools")
set(catkin_EXEC_DEPENDS "python-argparse" "python-catkin-pkg" "python3-catkin-pkg" "python-empy" "python3-empy")
set(catkin_EXEC_DEPENDS_python-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_EXEC_DEPENDS_python3-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_RUN_DEPENDS "python-argparse" "python-catkin-pkg" "python3-catkin-pkg" "python-empy" "python3-empy" "google-mock" "gtest" "python-nose" "python3-nose")
set(catkin_RUN_DEPENDS_python-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_RUN_DEPENDS_python3-catkin-pkg_VERSION_GT "0.4.3")
set(catkin_TEST_DEPENDS "python-mock" "python-nose" "python3-nose")
set(catkin_DOC_DEPENDS )
set(catkin_URL_WEBSITE "http://wiki.ros.org/catkin")
set(catkin_URL_BUGTRACKER "https://github.com/ros/catkin/issues")
set(catkin_URL_REPOSITORY "https://github.com/ros/catkin")
set(catkin_DEPRECATED "")