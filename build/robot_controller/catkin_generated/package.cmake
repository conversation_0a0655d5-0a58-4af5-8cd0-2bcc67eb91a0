set(_CATKIN_CURRENT_PACKAGE "robot_controller")
set(robot_controller_VERSION "0.0.0")
set(robot_controller_MAINTAINER "S1 <<EMAIL>>")
set(robot_controller_PACKAGE_FORMAT "2")
set(robot_controller_BUILD_DEPENDS "message_generation" "roscpp" "std_msgs" "serial" "visualization_msgs")
set(robot_controller_BUILD_EXPORT_DEPENDS "roscpp" "std_msgs" "serial" "visualization_msgs")
set(robot_controller_BUILDTOOL_DEPENDS "catkin")
set(robot_controller_BUILDTOOL_EXPORT_DEPENDS )
set(robot_controller_EXEC_DEPENDS "roscpp" "std_msgs" "serial" "message_runtime" "visualization_msgs")
set(robot_controller_RUN_DEPENDS "roscpp" "std_msgs" "serial" "message_runtime" "visualization_msgs")
set(robot_controller_TEST_DEPENDS )
set(robot_controller_DOC_DEPENDS )
set(robot_controller_URL_WEBSITE "")
set(robot_controller_URL_BUGTRACKER "")
set(robot_controller_URL_REPOSITORY "")
set(robot_controller_DEPRECATED "")