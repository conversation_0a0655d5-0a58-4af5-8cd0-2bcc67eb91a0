# generated from catkin/cmake/template/pkg.context.pc.in
CATKIN_PACKAGE_PREFIX = ""
PROJECT_PKG_CONFIG_INCLUDE_DIRS = "/home/<USER>/workspace/S1_robot/src/robot_controller/include".split(';') if "/home/<USER>/workspace/S1_robot/src/robot_controller/include" != "" else []
PROJECT_CATKIN_DEPENDS = "roscpp;std_msgs;message_runtime".replace(';', ' ')
PKG_CONFIG_LIBRARIES_WITH_PREFIX = "-lrobot_controller".split(';') if "-lrobot_controller" != "" else []
PROJECT_NAME = "robot_controller"
PROJECT_SPACE_DIR = "/home/<USER>/workspace/S1_robot/devel"
PROJECT_VERSION = "0.0.0"
